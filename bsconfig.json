{"name": "@wino/accounting", "package-specs": {"module": "commonjs", "in-source": true}, "bsc-flags": ["-bs-super-errors", "-open Belt", "-warn-error", "@A", "-w", "@A-4-9-20-30-40-41-42-102"], "bs-dependencies": [], "bs-dev-dependencies": ["@glennsl/bs-jest"], "sources": [{"dir": "src", "subdirs": true}, {"dir": "tests", "type": "dev", "subdirs": true}], "suffix": ".bs.js", "ppx-flags": [], "refmt": 3, "external-stdlib": "@rescript/std", "gentypeconfig": {"language": "typescript", "module": "commonjs", "importPath": "relative", "shims": {"ReasonPervasives": "ReasonPervasives"}, "debug": {"all": false, "basic": false}}}