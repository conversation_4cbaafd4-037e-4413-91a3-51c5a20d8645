module.exports = {
  preset: 'ts-jest',
  roots: ['<rootDir>/tests'],
  transformIgnorePatterns: [
    'node_modules/(?!(jest-)?bs-platform|@glennsl/|@wino/*)',
  ],
  testRegex: '.*(_|.)spec.ts$',
  transform: {
    '^.+\\.jsx?$': require.resolve('babel-jest'),
    '^.+\\.tsx?$': 'ts-jest',
  },
  moduleFileExtensions: ['tsx', 'js', 'ts', 'jsx'],
  globals: {
    'ts-jest': {
      diagnostics: false,
    },
  },
  setupFiles: ['<rootDir>/.jest/setup.js'],
}
