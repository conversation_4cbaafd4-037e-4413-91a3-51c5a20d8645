const { emptydir } = require('fs-extra')
const cwd = require('process').cwd()
const { join } = require('path')

const {
  findFilesByExt,
  createFolders,
  getBuildInfo,
  copyFile,
  updatePackageJSON,
} = require('./utils')

/**
 * 1. In cwd() generate a new version of whole module with `yarn version`
 * 2. Generate a new version of npmjs package (@wino/accounting)
 *    If the current branch is master -> vX.X.X
 *    If not -> 0.0.0-experimental-<commit-hash>
 * 3. Create recursively destination folders (bundle/(src|build))
 * 4. Analyses files to copy, push their full path to an array
 * 5. Copy those files to their destination
 */

const tsBuildDir = 'build'
const outDirBase = join(cwd, 'bundle')
const outputFolders = [join(outDirBase, 'src'), join(outDirBase, tsBuildDir)]

const package = [
  {
    sourcesFiles: [
      join(cwd, 'README.md'),
      join(cwd, 'package.json'),
      join(cwd, 'bsconfig.json'),
    ],
    destinationPath: outDirBase,
  },
  { // RES files
    sourcesFiles: [
      ...findFilesByExt(join(cwd, 'src'), 'res'),
      ...findFilesByExt(join(cwd, 'src'), 'js'),
    ],
    destinationPath: join(outDirBase, 'src'),
  },
  { // TS files
    sourcesFiles: [
      ...findFilesByExt(join(cwd, 'src'), 'js'),
      ...findFilesByExt(join(cwd, tsBuildDir), 'ts'),
      ...findFilesByExt(join(cwd, tsBuildDir), 'js'),
      ...findFilesByExt(join(cwd, tsBuildDir), 'map'),
    ],
    destinationPath: join(outDirBase, tsBuildDir),
  },
]

const lib = {
  name: '@wino/accounting',
  main: `${tsBuildDir}/index.js`,
  types: `${tsBuildDir}/index.d.ts`,
  package: package,
}

async function run() {
  const { version, isExperimental } = await getBuildInfo()

  await emptydir(outDirBase)
  await createFolders(outputFolders)

  for (const packageFiles of lib.package) {
    for await (const filePath of packageFiles.sourcesFiles) {
      let packageJsonPath = filePath.toString().includes('package.json')
      let destinationPath = packageFiles.destinationPath

      packageJsonPath ?
        await updatePackageJSON(filePath, destinationPath, {
          name: lib.name,
          main: lib.main,
          types: lib.types,
          version,
          isExperimental: false,
        })
        :
        await copyFile(filePath, destinationPath)
    }
  }

  return version
}

run()
  .then(function (version) {
    console.info(`📦  Version ${version} bundled successfully!`)
  })
  .catch(console.error)
