const fs = require('fs')
const cwd = require('process').cwd()
const { join } = require('path')
const { exec } = require('child-process-promise')
const { readJson, writeJson } = require('fs-extra')

async function execRead(command) {
  const { stdout } = await exec(command, { cwd })
  return stdout.trim()
}

async function copyFile(srcFile, destFolder) {
  return exec(`cp ${srcFile} ${destFolder}`, { cwd })
}

function findFilesByExt(base, ext, files = fs.readdirSync(base), result = []) {
  files.forEach(function (file) {
    const newbase = join(base, file)

    if (fs.statSync(newbase).isDirectory()) {
      result = findFilesByExt(newbase, ext, fs.readdirSync(newbase), result)
    } else {
      if (file.substr(-1 * (ext.length + 1)) == '.' + ext) {
        result.push(newbase)
      }
    }
  })

  return result
}

async function getLastCommitHash() {
  return execRead('git show -s --format=%h')
}

async function isExperimental() {
  const branch = await execRead('git symbolic-ref --short -q HEAD')
  return !['dev', 'master'].includes(branch)
}

async function getBuildInfo() {
  if (await isExperimental()) {
    const hash = await getLastCommitHash()

    return {
      isExperimental: true,
      version: `0.0.0-experimental-${hash}`,
    }
  } else {
    const packageJSON = await readJson(join(cwd, 'package.json'))
    const currentPackageVersion = packageJSON.version

    return {
      isExperimental: false,
      version: `v${currentPackageVersion}`,
    }
  }
}

async function createFolders(paths) {
  paths.forEach(async function (path) {
    await fs.mkdirSync(path, { recursive: true })
  })
}

async function updatePackageJSON(source, destinationFolder, options) {
  const packageJSON = await readJson(source)
  const tag = options.isExperimental ? 'experimental' : 'latest'

  packageJSON.name = options.name
  packageJSON.main = options.main
  packageJSON.version = options.version

  if (options.types) {
    packageJSON.types = options.types
  }

  delete packageJSON.scripts
  packageJSON.scripts = {
    'npm:publish': `npm publish . --tag ${tag}`,
  }

  return writeJson(join(destinationFolder, 'package.json'), packageJSON, {
    spaces: 2,
  })
}

module.exports = {
  findFilesByExt,
  createFolders,
  getBuildInfo,
  copyFile,
  updatePackageJSON,
}
