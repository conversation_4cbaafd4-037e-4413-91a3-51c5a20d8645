import type { t as Big_t } from './Big.gen'
import Big from 'big.js'

import type {
  cartInput,
  cart as GenCart,
  product as GenProduct,
  productStruct as GenProductStruct,
} from './Accounting__Types.gen'

import {
  make,
  compute as genCompute,
  serialize as genSerialize,
  deserialize as genDeserialize,
  fromRawProductQuantity,
  toRawProductQuantity,
} from './Accounting.gen'

type UnitProduct = GenProductStruct<number> & {
  capacityPrecision: null
  bulk: false
}

type BulkProduct = GenProductStruct<Big_t> & {
  capacityPrecision: number
  bulk: true
}

export type Product = UnitProduct | BulkProduct

export interface Cart extends Omit<GenCart, 'products'> {
  products: Product[]
}

export function _productFromGeneratedStructure(product: GenProduct): Product {
  switch (product.tag) {
    case 'Unit':
      return {
        ...product.value,
        capacityPrecision: null,
        bulk: false,
      }
    case 'Bulk':
      return {
        ...product.value[0],
        capacityPrecision: product.value[1],
        bulk: true,
      }
    default:
      // @ts-expect-error since it is just for runtime safety
      throw Error(`Product malformed with tag (${product.tag})`)
  }
}

export function _productToGeneratedStructure(product: Product): GenProduct {
  if (product.capacityPrecision) {
    const { capacityPrecision, ...rest } = product

    return {
      tag: 'Bulk',
      value: [rest, capacityPrecision],
    }
  }

  return {
    tag: 'Unit',
    // @ts-expect-error since we have issues with Big_t not assignable to number
    value: product,
  }
}

export function _make(cart: cartInput): Cart {
  const newCart = make(cart)

  return {
    ...newCart,
    products: newCart.products.map((product) =>
      _productFromGeneratedStructure(product),
    ),
  }
}

export function _compute(cart: Cart): Cart {
  const computedCart = genCompute({
    ...cart,
    products: cart.products.map((product) =>
      _productToGeneratedStructure(product),
    ),
  })

  return {
    ...computedCart,
    products: computedCart.products.map((product) =>
      _productFromGeneratedStructure(product),
    ),
  }
}

export function _serialize(cart: Cart): string {
  const cartOriginal = {
    ...cart,
    products: cart.products.map((product) =>
      _productToGeneratedStructure(product),
    ),
  }

  return genSerialize(cartOriginal)
}

export function _deserialize(cartStringified: string): Cart {
  const genCart = genDeserialize(cartStringified)

  return {
    ...genCart,
    products: genCart.products.map((product) =>
      _productFromGeneratedStructure(product),
    ),
  }
}

export function _isBulkProduct(product: Product): product is BulkProduct {
  return (
    product.capacityPrecision !== null &&
    product.capacityPrecision !== undefined
  )
}

export function _isUnitProduct(product: Product): product is UnitProduct {
  return !_isBulkProduct(product)
}

export function _cartIntegerToFloat(
  value: Big_t | number,
  precision?: number | null,
): number {
  return fromRawProductQuantity(
    { capacityPrecision: precision ?? undefined },
    new Big(value).toNumber(),
  ).toNumber()
}

export function _cartFloatToInteger(
  value: Big_t | number,
  precision?: number | null,
): number {
  return toRawProductQuantity(
    { capacityPrecision: precision ?? undefined },
    new Big(value),
  )
}
