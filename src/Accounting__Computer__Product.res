open! Big.Operators
open Accounting__Types

module Utils = Accounting__Utils
module ComputerDiscount = Accounting__Computer__Discount
module ComputerTax = Accounting__Computer__Tax

// Computes the total price of the product
let computeTotalPrice = (product: product, ~cart: cart): product =>
  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalPrice: Some(
        (product.quantity->Float.fromInt->Big.fromFloat *. product.unitPrice->Big.fromFloat)
          ->Big.round(cart.decimalPrecision),
      ),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalPrice: Some(
          (product.quantity *. product.unitPrice->Big.fromFloat)->Big.round(cart.decimalPrecision),
        ),
      },
      precision,
    )
  }

// Computes the amount of the
// discount applied to the product
let computeLocalDiscounts = (product: product, ~cart: cart): product => {
  let computeLocalDiscountWarnings = (discount: discount) =>
    switch product {
    | Unit({totalPrice}) | Bulk({totalPrice}, _) =>
      if discount.value > totalPrice->Option.getExn->Big.toFloat {
        [AmountGreaterThanTotalPrice]
      } else {
        []
      }
    }
  let computeLocalDiscount = discount => {
    let amount = discount->ComputerDiscount.Local.compute(~product, ~cart)
    let warnings = {...discount, amount}->computeLocalDiscountWarnings
    let newDiscount = {...discount, amount, warnings}

    {...newDiscount, formattedAmount: None, formattedValue: None}
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      discounts: product.discounts->Array.map(computeLocalDiscount),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        discounts: product.discounts->Array.map(computeLocalDiscount),
      },
      precision,
    )
  }
}

// Computes the amount of all the locals discounts
// Notice that, currently, product can only have one discount!
let computeTotalLocalDiscounts = (product: product, ~cart: cart): product => {
  let totalLocalDiscounts = switch product {
  | Unit({discounts}) | Bulk({discounts}, _) =>
    discounts
    ->Array.reduce(0.->Big.fromFloat, (acc, discount) => acc +. discount.amount->Option.getExn)
    ->Big.round(cart.decimalPrecision)
  }

  switch product {
  | Unit(product) => Unit({...product, totalLocalDiscounts: Some(totalLocalDiscounts)})
  | Bulk(product, precision) =>
    Bulk({...product, totalLocalDiscounts: Some(totalLocalDiscounts)}, precision)
  }
}

// Computes fees applied to a product
// Calculate all fees amount and format them
let computeFees = (product: product): product =>
  switch product {
  | Unit(product) =>
    let computeFee = (fee: fee) => {
      ...fee,
      formattedAmount: None,
      totalAmount: Some(
        fee.amount->Big.fromFloat *. product.quantity->Float.fromInt->Big.fromFloat,
      ),
      formattedTotalAmount: None,
    }

    Unit({
      ...product,
      fees: product.fees->Array.map(computeFee),
    })
  | Bulk(product, precision) =>
    let computeFee = (fee: fee) => {
      ...fee,
      formattedAmount: None,
      totalAmount: Some(fee.amount->Big.fromFloat *. product.quantity),
      formattedTotalAmount: None,
    }

    Bulk({...product, fees: product.fees->Array.map(computeFee)}, precision)
  }

// Computes the unit fee of a product
// Its represents the total of all fees amounts
let computeUnitFee = (product: product, ~cart: cart): product => {
  let unitFee = switch product {
  | Unit({fees}) | Bulk({fees}, _) =>
    fees
    ->Array.reduce(0.->Big.fromFloat, (acc, fee) => acc +. fee.amount->Big.fromFloat)
    ->Big.round(cart.decimalPrecision)
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      unitFee: Some(unitFee),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        unitFee: Some(unitFee),
      },
      precision,
    )
  }
}

// Compute the total fees
let computeTotalFees = (product: product, ~cart: cart): product =>
  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalFees: Some(
        (product.unitFee->Option.getExn *. product.quantity->Float.fromInt->Big.fromFloat)
          ->Big.round(cart.decimalPrecision),
      ),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalFees: Some(
          (product.unitFee->Option.getExn *. product.quantity)->Big.round(cart.decimalPrecision),
        ),
      },
      precision,
    )
  }

// Its provides the total duty (total hors taxes)
// including the product local discounts
// and excluding global discounts
let computeTotalAmountExcludingGlobalDiscounts = (product: product, ~cart: cart): product => {
  let totalAmountExcludingGlobalDiscounts = switch product {
  | Unit({totalPrice, totalFees, totalLocalDiscounts})
  | Bulk({totalPrice, totalFees, totalLocalDiscounts}, _) =>
    (totalPrice->Option.getExn +. totalFees->Option.getExn -. totalLocalDiscounts->Option.getExn)
      ->Big.round(cart.decimalPrecision)
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalAmountExcludingGlobalDiscounts: Some(totalAmountExcludingGlobalDiscounts),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalAmountExcludingGlobalDiscounts: Some(totalAmountExcludingGlobalDiscounts),
      },
      precision,
    )
  }
}
// Since the global discounts affects each product
// this function computes how high the global
// discounts lower the total duty of a certain product
let computeGlobalDiscounts = (product: product, ~cart: cart): product => {
  let ratio = if cart.totalAmountExcludingGlobalDiscounts->Option.getExn->Big.toFloat <= 0. {
    1.->Big.fromFloat
  } else {
    switch product {
    | Unit({totalAmountExcludingGlobalDiscounts})
    | Bulk({totalAmountExcludingGlobalDiscounts}, _) =>
      totalAmountExcludingGlobalDiscounts->Option.getExn /.
        cart.totalAmountExcludingGlobalDiscounts->Option.getExn
    }
  }
  let totalGlobalDiscounts =
    (cart.discounts->Array.reduce(0.->Big.fromFloat, (acc, discount) =>
      acc +. discount.amount->Option.getExn
    ) *. ratio)->Big.round(cart.decimalPrecision)

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalGlobalDiscounts: Some(totalGlobalDiscounts),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalGlobalDiscounts: Some(totalGlobalDiscounts),
      },
      precision,
    )
  }
}

// Provides the total amount of
// discounts applied to a product
let computeTotalDiscounts = (product: product) => {
  let totalDiscounts = switch product {
  | Unit({totalLocalDiscounts, totalGlobalDiscounts})
  | Bulk({totalLocalDiscounts, totalGlobalDiscounts}, _) =>
    totalLocalDiscounts->Option.getExn +. totalGlobalDiscounts->Option.getExn
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalDiscounts: Some(totalDiscounts),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalDiscounts: Some(totalDiscounts),
      },
      precision,
    )
  }
}

// Loops over the product's taxes and computes their amounts
let computeTaxesAmounts = (product: product, ~cart: cart): product => {
  let taxes = switch product {
  | Unit({taxes}) | Bulk({taxes}, _) => taxes
  }
  let productTax = taxes->Option.getExn->Array.getExn(0)

  let mutatedTaxes = if !cart.taxesFree {
    let totalAmountExcludingTransportFee =
      productTax->ComputerTax.computeTotalAmountExcludingTransportFee(
        ~product,
        ~decimalPrecision=cart.decimalPrecision,
        (),
      )
    let transportAmount = ComputerTax.computeTransportFeeTaxAmount(
      ~product,
      ~decimalPrecision=cart.decimalPrecision,
      ~standardTaxRate=cart.standardTaxRate,
    )
    let totalAmount = switch (totalAmountExcludingTransportFee, transportAmount) {
    | (Some(amount), Some(transportAmount)) => Some(amount +. transportAmount)
    | _ => totalAmountExcludingTransportFee
    }
    Some(
      if productTax.rate === cart.standardTaxRate {
        [
          {
            rate: productTax.rate,
            amount: totalAmount,
            formattedAmount: None,
          },
        ]
      } else {
        [
          {
            rate: productTax.rate,
            amount: totalAmountExcludingTransportFee,
            formattedAmount: None,
          },
          {
            rate: cart.standardTaxRate,
            amount: transportAmount,
            formattedAmount: None,
          },
        ]
      },
    )
  } else {
    taxes->Option.map(taxes =>
      taxes->Array.map(tax => {
        rate: tax.rate,
        amount: Some(0.->Big.fromFloat),
        formattedAmount: None,
      })
    )
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      taxes: mutatedTaxes,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        taxes: mutatedTaxes,
      },
      precision,
    )
  }
}

let computeTotalTaxesExcludingGlobalDiscount = (product: product, ~cart: cart): product => {
  let taxes = switch product {
  | Unit({taxes}) | Bulk({taxes}, _) => taxes
  }
  let productTax = taxes->Option.getExn->Array.getExn(0)

  let totalTaxesExcludingGlobalDiscount = if !cart.taxesFree {
    let totalAmountExcludingTransportFeeExcludingGlobalDiscount =
      productTax->ComputerTax.computeTotalAmountExcludingTransportFee(
        ~product,
        ~decimalPrecision=cart.decimalPrecision,
        ~excludingGlobalDiscount=true,
        (),
      )
    let transportAmount = ComputerTax.computeTransportFeeTaxAmount(
      ~product,
      ~decimalPrecision=cart.decimalPrecision,
      ~standardTaxRate=cart.standardTaxRate,
    )
    switch (totalAmountExcludingTransportFeeExcludingGlobalDiscount, transportAmount) {
    | (Some(amount), Some(transportAmount)) => Some(amount +. transportAmount)
    | _ => totalAmountExcludingTransportFeeExcludingGlobalDiscount
    }
  } else {
    Some(Big.fromFloat(0.))
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalTaxesExcludingGlobalDiscount,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalTaxesExcludingGlobalDiscount,
      },
      precision,
    )
  }
}

let computeTotalTaxes = (product: product, ~cart: cart): product => {
  let totalTaxes = product->ComputerTax.computeTotalAmount(~cart)

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalTaxes,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalTaxes,
      },
      precision,
    )
  }
}

// Computes the unit cost
let computeUnitCost = (product: product, ~cart: cart): product =>
  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      unitCost: product.quantity === 0
        ? None
        : Some(
            (product.totalAmountExcludingTaxes->Option.getExn /.
              product.quantity->Float.fromInt->Big.fromFloat)->Big.round(cart.decimalPrecision),
          ),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        unitCost: product.quantity->Big.toFloat === 0.
          ? None
          : Some(
              (product.totalAmountExcludingTaxes->Option.getExn /. product.quantity)
                ->Big.round(cart.decimalPrecision),
            ),
      },
      precision,
    )
  }

// Keep available fee kinds field up to date
// by calling this function
let computeAvailableFeeKinds = (product: product) => {
  let availablesFeeKinds = product->Utils.getAvailableFeeKinds

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      availablesFeeKinds: Some(availablesFeeKinds),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        availablesFeeKinds: Some(availablesFeeKinds),
      },
      precision,
    )
  }
}

// Compute expected quantity warning
let computeExpectedQuantityWarning = (product: product) =>
  switch product {
  | Unit(product) => {
      let product = switch product.packaging {
      | Some(packaging) =>
        let expectedQuantity = product.expectedQuantity->Float.fromInt->Big.fromFloat
        let packaging = packaging->Float.fromInt->Big.fromFloat

        {
          ...product,
          expectedQuantityWarning: expectedQuantity->Utils.isMultipleOf(packaging)
            ? []
            : [IsNotMultipleOfPackaging],
        }
      | _ => product
      }

      Unit(product)
    }

  | Bulk(product, precision) => {
      let product = switch product.packaging {
      | Some(packaging) => {
          ...product,
          expectedQuantityWarning: product.expectedQuantity->Utils.isMultipleOf(packaging)
            ? []
            : [IsNotMultipleOfPackaging],
        }
      | _ => product
      }

      Bulk(product, precision)
    }
  }

let computeTotalAmounts = (product: product, ~cart: cart) => {
  let totalAmount = product->ComputerTax.computeTotalAmountExcludingTaxes(~cart)
  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalAmountExcludingTaxes: totalAmount,
      totalAmountIncludingTaxes: None,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalAmountExcludingTaxes: totalAmount,
        totalAmountIncludingTaxes: None,
      },
      precision,
    )
  }
}

let computeTotalAmountWithTaxes = (product: product, ~cart: cart) => {
  let totalAmount = product->ComputerTax.computeTotalAmountIncludingTaxes(~cart)
  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      totalAmountIncludingTaxes: totalAmount,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        totalAmountIncludingTaxes: totalAmount,
      },
      precision,
    )
  }
}

// Batch all computes before applying global discounts
let preCompute = (product: product, ~cart: cart): product =>
  product
  // Step 1: Compute initial values
  ->computeAvailableFeeKinds
  ->computeExpectedQuantityWarning
  ->computeTotalPrice(~cart)
  ->computeLocalDiscounts(~cart)
  ->computeTotalLocalDiscounts(~cart)
  ->computeFees
  ->computeUnitFee(~cart)
  ->computeTotalFees(~cart)
  ->computeTotalAmountExcludingGlobalDiscounts(~cart)

// Batch all computes after global discounts
// application to provide a confident product
let postCompute = (product: product, ~cart: cart): product =>
  product
  // Step 2: Compute global discounts and apply it
  ->computeGlobalDiscounts(~cart)
  ->computeTotalDiscounts
  // Step 3: Compute totalAmount(Excluding|Including)Taxes
  // depending on cart.taxesIncluded
  ->computeTotalAmounts(~cart)
  // Step 4: Compute the unit cost
  // based on the totalDiscounts and the totalFees
  ->computeUnitCost(~cart)
  // Step 5: Compute taxes, their total amounts
  // and the total amount of all taxes
  ->computeTaxesAmounts(~cart)
  ->computeTotalTaxesExcludingGlobalDiscount(~cart)
  ->computeTotalTaxes(~cart)
  // Step 6: Take into account the taxes (totalAmountOfTaxes)
  // in the totalAmount(Excluding|Including)Taxes
  ->computeTotalAmountWithTaxes(~cart)
