open! Big.Operators
open Accounting__Types
open Accounting__Exception

module Global = {
  let computePercent = (discount: discount, ~cart: cart) => {
    let totalAmountOfGoodsExcludingGlobalDiscounts = cart.products->Array.reduce(
      Big.fromFloat(0.),
      (acc, product) =>
        switch product {
        | Unit({totalPrice: Some(totalPrice), totalLocalDiscounts: Some(totalLocalDiscounts)})
        | Bulk({totalPrice: Some(totalPrice), totalLocalDiscounts: Some(totalLocalDiscounts)}, _) =>
          acc +. totalPrice -. totalLocalDiscounts
        | _ => acc
        },
    )
    Some(
      (discount.value->Big.fromFloat /.
      100.->Big.fromFloat *.
      totalAmountOfGoodsExcludingGlobalDiscounts)->Big.round(cart.decimalPrecision),
    )
  }

  let computeCurrency = (discount: discount, ~cart: cart) =>
    if discount.value > cart.totalAmountExcludingGlobalDiscounts->Option.getExn->Big.toFloat {
      cart.totalAmountExcludingGlobalDiscounts
    } else {
      Some(discount.value->Big.fromFloat->Big.round(cart.decimalPrecision))
    }

  let computeFree = (_discount: discount) =>
    raise(NotPermitted("Free discount should not be applied globally!"))

  let compute = (discount: discount, ~cart: cart) =>
    switch discount.kind {
    // At this version, Free discount is not permitted
    | Free => discount->computeFree
    // If discount is a Percent discount,
    // the amount of the discount should be
    // the .value of the discount percented multiplied
    // by the totalAmount of the product
    | Percent => discount->computePercent(~cart)
    //  When discount kind is Currency the discount amount
    // is now the discount.value
    | Currency => discount->computeCurrency(~cart)
    }
}

module Local = {
  let computePercent = (discount: discount, ~product: product, ~cart: cart) =>
    switch product {
    | Unit({totalPrice})
    | Bulk({totalPrice}, _) =>
      Some(
        (discount.value->Big.fromFloat /. 100.->Big.fromFloat *. totalPrice->Option.getExn)
          ->Big.round(cart.decimalPrecision),
      )
    }

  let computeCurrency = (discount: discount, ~product: product, ~cart: cart) =>
    switch product {
    | Unit({totalPrice})
    | Bulk({totalPrice}, _) =>
      if discount.value > totalPrice->Option.getExn->Big.toFloat {
        totalPrice
      } else {
        Some(discount.value->Big.fromFloat->Big.round(cart.decimalPrecision))
      }
    }

  let computeFree = (discount: discount, ~product: product, ~cart: cart) =>
    switch product {
    | Unit({unitPrice})
    | Bulk({unitPrice}, _) =>
      Some((unitPrice->Big.fromFloat *. discount.quantity)->Big.round(cart.decimalPrecision))
    }

  let compute = (discount: discount, ~product: product) =>
    switch discount.kind {
    | Percent => discount->computePercent(~product)
    | Currency => discount->computeCurrency(~product)
    | Free => discount->computeFree(~product)
    }
}
