open Accounting__Types
open Accounting__Exception

module Utils = Accounting__Utils
module Maker = Accounting__Maker
module Validator = Accounting__Validator

// Adds a product to the cart
// If the cart contains already this product
//    the quantity is summed
let addProduct = (cart, productInput: productInput): cart => {
  let product = productInput->Maker.Product.make
  let (productId, productIdentifier) = switch product {
  | Unit({id, identifier}) | Bulk({id, identifier}, _) => (id, identifier)
  }
  {
    ...cart,
    products: cart->Utils.cartHasProduct(productIdentifier)
      ? cart.products->Array.map(current => {
          let identified = switch current {
          | Unit({id}) | Bulk({id}, _) => id === productId
          }

          identified
            ? switch (current, product) {
              | (Unit(current), Unit(product)) =>
                let quantity = current.quantity + product.quantity

                Unit({...current, quantity, expectedQuantity: quantity})
              | (Bulk(current, precision), Bulk(product, _)) =>
                open! Big.Operators
                let quantity = current.quantity +. product.quantity

                Bulk({...current, quantity, expectedQuantity: quantity}, precision)
              | _ => failwith("addProduct: input product identifier")
              }
            : current
        })
      : [product]->Array.concat(cart.products),
  }
}

// Adds a bunch of products to the cart
let addProducts = (cart, productInputs: array<productInput>): cart =>
  productInputs->Array.reduce(cart, (acc, productInput) => acc->addProduct(productInput))

// Removes a product from the cart
// The product is identified by (.id|.stockKeepingUnit)
let removeProduct = (cart, key): cart => {
  ...cart,
  products: cart.products->Array.keep(product =>
    !(product->Utils.productIsIdentifiedBy(~key=Some(key)))
  ),
}

// Updates a product
// It's thought to support all kinds
// of update operation on a product
// The last parameter is a function which take
// a product as parameter and return a product 🙃
// It also have a kind of gateway
//  the update's.name can not be empty
//  the update's.description can not be empty
//  the update's.unitPrice can not be negative
//  the update's.quantity can not be negative
let updateProduct = (cart, key, update: product => product): cart => {
  ...cart,
  products: cart.products->Array.map(product =>
    if product->Utils.productIsIdentifiedBy(~key=Some(key)) {
      let newProduct = update(product)

      switch newProduct->Validator.Product.validate() {
      | Ok() => newProduct
      | Error(_error) => product
      }
    } else {
      product
    }
  ),
}

// Batch all products update with the same update function
let updateAllProducts = (cart, update: product => product) => {
  ...cart,
  products: cart.products->Array.map(update),
}

// Adds a fee to a product
// If a fee is provided as parameter
//    we add it
// else
//    we generate a new fee to add
// Sure, we do the above only if the product
// has available fee kinds
let addProductFee = (~fee: option<fee>=?, product: product, ()) => {
  let makeFees = () =>
    switch product {
    | Unit({fees, availablesFeeKinds}) | Bulk({fees, availablesFeeKinds}, _) =>
      switch fee {
      | Some(fee) if product->Utils.canAcceptFeeKind(~feeKind=fee.kind) =>
        fees->Array.concat([{...fee, id: Utils.makeUUID()}])
      | _ =>
        let feeInput = {
          id: None,
          kind: availablesFeeKinds->Option.getExn->Array.getExn(0),
          amount: 0.,
        }

        fees->Array.concat([feeInput->Maker.Fee.make])
      }
    }

  switch (product->Utils.hasAvailableFeeKinds, product) {
  | (true, Unit(product)) =>
    Unit({
      ...product,
      fees: makeFees(),
    })
  | (true, Bulk(product, precision)) =>
    Bulk(
      {
        ...product,
        fees: makeFees(),
      },
      precision,
    )
  | _ => product
  }
}

// Updates a fee in a product
let updateProductFee = (id, update: fee, product: product) => {
  let fees = switch product {
  | Unit({fees}) | Bulk({fees}, _) =>
    fees->Array.map(fee =>
      fee.id === id
        ? {
            ...fee,
            amount: update.amount,
            kind: update.kind,
          }
        : fee
    )
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      fees,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        fees,
      },
      precision,
    )
  }
}

// Replicates a fee to the product in parameters
// Don't forget that a product should have only one feeKind.
// So we check if the product has already the feeKind
//    we update the current fee in this case
// else
//    we add it simply
let replicateFeeToProduct = (fee: fee, product: product) =>
  switch product {
  | Unit({fees}) | Bulk({fees}, _) =>
    switch fees->Array.keep(productFee => productFee.kind === fee.kind) {
    | [] => addProductFee(~fee, product, ())
    | [existingKindFee] => product |> updateProductFee(existingKindFee.id, fee)
    | _ => raise(NotPermitted("Product should have unique fee kind"))
    }
  }

// Removes a fee from a product
let removeProductFee = (id, product: product) =>
  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      fees: product.fees->Array.keep(fee => fee.id !== id),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        fees: product.fees->Array.keep(fee => fee.id !== id),
      },
      precision,
    )
  }

// Adds a discount to a product
// Before do that, we check if the incoming discount
// can be applied to the product
// if not -> no application of discount
let addProductDiscount = (discountInput: discountInput, product: product) => {
  let (capacityPrecision, productDiscounts) = switch product {
  | Bulk({discounts}, precision) => (Some(precision), discounts)
  | Unit({discounts}) => (None, discounts)
  }
  let makeDiscounts = (~productQuantity) => {
    let discount = discountInput->Maker.Discount.make(~capacityPrecision?)
    let discountAppendable = discount->Validator.Discount.localDiscountCanBeAddedTo(~product)

    switch discountAppendable {
    | true =>
      let discountQuantity = discount.kind === Free ? discount.quantity : productQuantity

      productDiscounts->Array.concat([
        {
          ...discount,
          quantity: discountQuantity,
        },
      ])
    | false => productDiscounts
    }
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      discounts: makeDiscounts(~productQuantity=product.quantity->Float.fromInt->Big.fromFloat),
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        discounts: makeDiscounts(~productQuantity=product.quantity),
      },
      precision,
    )
  }
}

// Updates a discount in a product
// Here too, we need to check if the update discount object
// can be applied to the product
let updateProductDiscount = (id, update: discount, product: product) => {
  let discountsUpdated = switch product {
  | Unit({discounts}) | Bulk({discounts}, _) =>
    let discountUpdatable = update->Validator.Discount.localDiscountCanBeUpdatedIn(~product)

    switch discountUpdatable {
    | true =>
      discounts->Array.map(discount =>
        discount.id === id
          ? {
              ...discount,
              name: update.name,
              kind: update.kind,
              value: update.value,
              quantity: update.quantity,
            }
          : discount
      )
    | false => discounts
    }
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      discounts: discountsUpdated,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        discounts: discountsUpdated,
      },
      precision,
    )
  }
}

// Removes a discount from the product
let removeProductDiscount = (id, product: product) => {
  let discountsUpdated = switch product {
  | Unit({discounts}) | Bulk({discounts}, _) =>
    discounts->Array.keep(discount => discount.id !== id)
  }

  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      discounts: discountsUpdated,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        discounts: discountsUpdated,
      },
      precision,
    )
  }
}

// Updates some products in the cart
// partially (just a field in the product)
let updateSomeProductsPartially = (
  cart,
  keysAndPartials: array<(string, 'a)>,
  update: (product, 'a) => product,
) =>
  keysAndPartials->Array.reduce(cart, (cart, (key, partial)) =>
    cart->updateProduct(key, product => update(product, partial))
  )

// Adds a global discount to the cart
// Also check if the global discount can be applied
let addGlobalDiscount = (cart, discountInput: discountInput) => {
  let discount = discountInput->Maker.Discount.make

  {
    ...cart,
    discounts: discount->Validator.Discount.globalDiscountCanBeAddedTo(~cart)
      ? cart.discounts->Array.concat([{...discount, id: Utils.makeUUID()}])
      : cart.discounts,
  }
}

// Removes a global discount from the cart
let removeGlobalDiscount = (cart, id) => {
  ...cart,
  discounts: cart.discounts->Array.keep(discount => discount.id !== id),
}

// Updates a global discount in the cart
// Checks if the update discount can be applied before
let updateGlobalDiscount = (cart, id, update) => {
  ...cart,
  discounts: update->Validator.Discount.globalDiscountCanBeAddedTo(~cart)
    ? cart.discounts->Array.map(discount =>
        discount.id === id
          ? {
              ...discount,
              name: update.name,
              kind: update.kind,
              value: update.value,
              quantity: update.quantity,
            }
          : discount
      )
    : cart.discounts,
}
