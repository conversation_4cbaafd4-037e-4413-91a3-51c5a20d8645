open Accounting__Types
open Accounting__Actions
open Accounting__Exception

module Mutations = Accounting__Mutations
module Computer = Accounting__Computer
module Formatter = Accounting__Formatter
module Utils = Accounting__Utils

// Shortcut of computing, formatting, optimizing cart
let process = (oldCart, pendingCart) => {
  let newCart = pendingCart->Computer.make->Formatter.make

  Accounting__Optimizer.make(oldCart, newCart)
}

// Main reducer of the module
// After actions which involve computing
// we always call the ->compute->format function to keep
// all fields up to date and reliable
let reducer = cart => {
  action =>
    switch action {
    | ProductAdded(input) => process(cart, cart->Mutations.addProduct(input))
    | BatchProductAdded(inputs) => process(cart, cart->Mutations.addProducts(inputs))
    | ProductRemoved(key) => process(cart, cart->Mutations.removeProduct(key))
    | ProductQuantityUpdated(key, quantity) =>
      process(
        cart,
        cart->Mutations.updateProduct(key, product =>
          switch (product, quantity) {
          | (Unit(product), UnitQuantity(quantity)) =>
            Unit({
              ...product,
              quantity,
            })
          | (Bulk(product, precision), BulkQuantity(quantity)) =>
            Bulk(
              {
                ...product,
                quantity: quantity->Big.fromFloat,
              },
              precision,
            )
          | _ =>
            raise(
              NotPermitted("The passed quantity kind doesn't match with the found product by key"),
            )
          }
        ),
      )
    | ProductExpectedQuantityUpdated(key, expectedQuantity) =>
      process(
        cart,
        cart->Mutations.updateProduct(key, product =>
          switch (product, expectedQuantity) {
          | (Unit(product), UnitQuantity(quantity)) =>
            Unit({
              ...product,
              expectedQuantity: quantity,
              quantity,
            })
          | (Bulk(product, precision), BulkQuantity(quantity)) =>
            Bulk(
              {
                ...product,
                expectedQuantity: quantity->Big.fromFloat,
                quantity: quantity->Big.fromFloat,
              },
              precision,
            )
          | _ =>
            raise(
              NotPermitted("The passed quantity kind doesn't match with the found product by key"),
            )
          }
        ),
      )
    | BatchProductExpectedQuantityUpdated(keysAndExpectedQuantities) =>
      process(
        cart,
        cart->Mutations.updateSomeProductsPartially(keysAndExpectedQuantities, (
          product,
          expectedQuantity,
        ) =>
          switch (product, expectedQuantity) {
          | (Unit(product), UnitQuantity(quantity)) =>
            Unit({
              ...product,
              expectedQuantity: quantity,
              quantity,
            })
          | (Bulk(product, precision), BulkQuantity(quantity)) =>
            Bulk(
              {
                ...product,
                expectedQuantity: quantity->Big.fromFloat,
                quantity: quantity->Big.fromFloat,
              },
              precision,
            )
          | _ =>
            raise(
              NotPermitted("The passed quantity kind doesn't match with the found product by key"),
            )
          }
        ),
      )
    | ProductUnitPriceUpdated(key, unitPrice) =>
      process(
        cart,
        cart->Mutations.updateProduct(key, product =>
          switch product {
          | Unit(product) => Unit({...product, unitPrice})
          | Bulk(product, precision) => Bulk({...product, unitPrice}, precision)
          }
        ),
      )
    | ProductNameUpdated(key, name) =>
      cart->Mutations.updateProduct(key, product =>
        switch product {
        | Unit(product) => Unit({...product, name})
        | Bulk(product, precision) => Bulk({...product, name}, precision)
        }
      )
    | ProductDescriptionUpdated(key, description) =>
      cart->Mutations.updateProduct(key, product =>
        switch product {
        | Unit(product) => Unit({...product, description})
        | Bulk(product, precision) => Bulk({...product, description}, precision)
        }
      )
    | ProductFeeAdded(key) =>
      process(
        cart,
        cart->Mutations.updateProduct(key, product => product->Mutations.addProductFee()),
      )
    | ProductFeeUpdated(key, id, fee) =>
      process(cart, cart->Mutations.updateProduct(key, Mutations.updateProductFee(id, fee)))
    | ProductFeeReplicated(fee) =>
      process(cart, cart->Mutations.updateAllProducts(fee->Mutations.replicateFeeToProduct))
    | ProductFeeRemoved(key, id) =>
      process(cart, cart->Mutations.updateProduct(key, Mutations.removeProductFee(id)))
    | ProductsFeePerUnitAllocated(globalFeeAmount) =>
      let productsTotalUnits =
        Accounting__Computer__Cart.computeTotalProductsUnits(cart)->Big.toFloat
      process(
        cart,
        cart->Mutations.updateAllProducts(product => {
          let fee = Accounting__Maker.Fee.makeTransportFeePerUnit(
            ~globalFeeAmount,
            ~productsTotalUnits,
          )
          fee->Mutations.replicateFeeToProduct(product)
        }),
      )
    | ProductsFeeProratedByPriceAllocated(globalFeeAmount) =>
      process(
        cart,
        cart->Mutations.updateAllProducts(product => {
          let productQuantity = switch product {
          | Unit(product) => Float.fromInt(product.quantity)
          | Bulk(product, _) => Big.toFloat(product.quantity)
          }
          let totalProductPriceExcludingDiscount = switch product {
          | Unit(product) => product.totalPrice
          | Bulk(product, _) => product.totalPrice
          }->Option.mapWithDefault(0., Big.toFloat)
          let totalCartPriceExcludingGlobalDiscount =
            cart.totalAmountOfGoods->Option.mapWithDefault(0., value => {
              let totalCartDiscounts = cart.totalDiscounts->Option.getWithDefault(Big.fromFloat(0.))
              let totalAmountOfGoodsExcludingDiscounts = value->Big.plus(totalCartDiscounts)
              totalAmountOfGoodsExcludingDiscounts->Big.toFloat
            })
          let fee = Accounting__Maker.Fee.makeTransportFeeProratedByPrice(
            ~globalFeeAmount,
            ~productQuantity,
            ~totalProductPriceExcludingDiscount,
            ~totalCartPriceExcludingGlobalDiscount,
          )
          fee->Mutations.replicateFeeToProduct(product)
        }),
      )
    | ProductDiscountAdded(key, input) =>
      process(cart, cart->Mutations.updateProduct(key, Mutations.addProductDiscount(input)))
    | ProductDiscountUpdated(key, id, discount) =>
      process(
        cart,
        cart->Mutations.updateProduct(key, Mutations.updateProductDiscount(id, discount)),
      )
    | ProductDiscountRemoved(key, id) =>
      process(cart, cart->Mutations.updateProduct(key, Mutations.removeProductDiscount(id)))
    | GlobalDiscountAdded(input) => process(cart, cart->Mutations.addGlobalDiscount(input))
    | GlobalDiscountRemoved(id) => process(cart, cart->Mutations.removeGlobalDiscount(id))
    | GlobalDiscountUpdated(id, discount) =>
      process(cart, cart->Mutations.updateGlobalDiscount(id, discount))
    | StandardTaxRateUpdated(vat) => process(cart, {...cart, standardTaxRate: vat})
    | TaxesFreeToggleRequested => process(cart, {...cart, taxesFree: !cart.taxesFree})
    }
}
