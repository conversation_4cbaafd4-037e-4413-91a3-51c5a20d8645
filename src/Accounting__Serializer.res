open Accounting__Types
open Accounting__Exception

module Maker = Accounting__Maker
module Computer = Accounting__Computer
module Formatter = Accounting__Formatter
module Utils = Accounting__Utils

type json

// Just an alias of JSON.parse of Javascript
let parse: string => json = %raw(`(cart) => JSON.parse(cart)`)

// Just an alias of JSON.stringify of Javascript
let stringify = %raw(`(cart) => JSON.stringify(cart)`)

// Object shapes when they are serialized (stringified if you prefer 😜)
module SerializedTypes = {
  type fee = {
    id: string,
    kind: string,
    amount: string,
  }

  type discount = {
    id: string,
    name: string,
    kind: string,
    value: string,
    quantity: int,
  }

  type tax = {rate: string}

  type productStruct<'value> = {
    id: string,
    identifier: option<string>,
    stockKeepingUnit: option<string>,
    name: string,
    description: string,
    capacityUnit: option<string>,
    stock: 'value,
    packaging: option<'value>,
    quantity: 'value,
    expectedQuantity: 'value,
    unitPrice: string,
    fees: array<fee>,
    discounts: array<discount>,
    taxes: array<tax>,
  }

  type product =
    | Unit({product: productStruct<int>})
    | Bulk({product: productStruct<float>, precision: int})

  type cartInput = {
    products: array<product>,
    discounts: array<discount>,
    decimalPrecision: int,
    currency: string,
    taxesFree: bool,
    standardTaxRate: float,
  }
}

module Currency = {
  // Serialize currency type
  let serialize = (input: currency) =>
    switch input {
    | Eur => "eur"
    | Usd => "usd"
    | Pound => "pound"
    }

  // Deserialize currency type
  let deserialize = (input): currency =>
    switch input {
    | "usd" => Usd
    | "eur" => Eur
    | _ => raise(NotFound("Currency type not found"))
    }
}

module Tax = {
  // Deserialize a tax to have a productTax that can be passed into productInput
  let deserialize = (tax: SerializedTypes.tax, ~decimalPrecision): tax =>
    {
      rate: tax.rate->Js.Float.fromString,
      amount: None,
      formattedAmount: None,
    }->Formatter.ProductTax.make(~currency=Eur, ~precision=Some(decimalPrecision))

  // Accounting.cart.tax -> tax that can be serialized
  let serialize = (tax: tax): SerializedTypes.tax => {
    rate: tax.rate->Js.Float.toString,
  }
}

module Discount = {
  // Serialize discount type
  let serializeKind = (input: discountKind) =>
    switch input {
    | Currency => "currency"
    | Percent => "percent"
    | Free => "free"
    }

  // Deserialize discount type
  let deserializeKind = (input): discountKind =>
    switch input {
    | "currency" => Currency
    | "free" => Free
    | "percent" => Percent
    | _ => raise(NotFound("Discount type not found"))
    }

  // Deserialize a discount to have a discountInput that can be passed into productInput
  let deserialize = (discount: SerializedTypes.discount): discountInput => {
    id: Some(discount.id),
    name: discount.name,
    kind: discount.kind->deserializeKind,
    value: discount.value->Js.Float.fromString,
    quantity: discount.quantity,
  }

  // Accounting.cart.discount -> discount that can be serialized
  let serialize = (
    ~capacityPrecision: option<int>=?,
    discount: discount,
  ): SerializedTypes.discount => {
    id: discount.id,
    name: discount.name,
    kind: discount.kind->serializeKind,
    value: discount.value->Js.Float.toString,
    quantity: discount.quantity->Utils.toRawProductQuantity(~capacityPrecision?),
  }
}

module Fee = {
  // Serialize fees kind type
  let serializeKind = (input: feeKind) =>
    switch input {
    | Transport => "transport"
    | Taxes => "taxes"
    | Other => "other"
    }

  // Deserialize fees kind type
  let deserializeKind = (input): feeKind =>
    switch input {
    | "taxes" => Taxes
    | "other" => Other
    | "transport" => Transport
    | _ => raise(NotFound("Fee kind not found"))
    }

  // Deserialize a fee to have a feeInput that can be passed into productInput
  let deserialize = (fee: SerializedTypes.fee): feeInput => {
    id: Some(fee.id),
    kind: fee.kind->deserializeKind,
    amount: fee.amount->Js.Float.fromString,
  }

  // Accounting.cart.fee -> fee that can be serialized
  let serialize = (fee: fee): SerializedTypes.fee => {
    id: fee.id,
    kind: fee.kind->serializeKind,
    amount: fee.amount->Js.Float.toString,
  }
}

module Product = {
  let deserialize = (
    product: SerializedTypes.product,
    ~cart: SerializedTypes.cartInput,
  ): productInput =>
    switch product {
    | Unit({product}) =>
      Unit({
        product: {
          id: Some(product.id),
          identifier: product.identifier,
          stockKeepingUnit: product.stockKeepingUnit,
          name: product.name,
          description: product.description,
          capacityUnit: product.capacityUnit,
          stock: product.stock,
          packaging: product.packaging,
          quantity: product.quantity,
          expectedQuantity: Some(product.expectedQuantity),
          unitPrice: product.unitPrice->Js.Float.fromString,
          tax: switch product.taxes
          ->Array.map(Tax.deserialize(~decimalPrecision=cart.decimalPrecision))
          ->Array.get(0) {
          | Some(tax) => tax.rate
          | None => 0.
          },
          fees: product.fees->Array.map(Fee.deserialize),
          discounts: product.discounts->Array.map(Discount.deserialize),
        },
      })
    | Bulk({product, precision}) =>
      Bulk({
        product: {
          id: Some(product.id),
          identifier: product.identifier,
          stockKeepingUnit: product.stockKeepingUnit,
          name: product.name,
          description: product.description,
          capacityUnit: product.capacityUnit,
          stock: product.stock,
          packaging: product.packaging,
          quantity: product.quantity,
          expectedQuantity: Some(product.expectedQuantity),
          unitPrice: product.unitPrice->Js.Float.fromString,
          tax: switch product.taxes
          ->Array.map(Tax.deserialize(~decimalPrecision=cart.decimalPrecision))
          ->Array.get(0) {
          | Some(tax) => tax.rate
          | None => 0.
          },
          fees: product.fees->Array.map(Fee.deserialize),
          discounts: product.discounts->Array.map(Discount.deserialize),
        },
        precision,
      })
    }

  // Accounting.cart.product -> product that can be serialized
  let serialize = (product: product): SerializedTypes.product =>
    switch product {
    | Unit(product) =>
      Unit({
        product: {
          id: product.id,
          identifier: product.identifier,
          stockKeepingUnit: product.stockKeepingUnit,
          name: product.name,
          description: product.description,
          capacityUnit: product.capacityUnit,
          stock: product.stock,
          packaging: product.packaging,
          quantity: product.quantity,
          expectedQuantity: product.expectedQuantity,
          unitPrice: product.unitPrice->Js.Float.toString,
          taxes: product.taxes->Option.getExn->Array.map(Tax.serialize),
          fees: product.fees->Array.map(Fee.serialize),
          discounts: product.discounts->Array.map(discount => discount->Discount.serialize),
        },
      })
    | Bulk(product, precision) =>
      Bulk({
        product: {
          id: product.id,
          identifier: product.identifier,
          stockKeepingUnit: product.stockKeepingUnit,
          name: product.name,
          description: product.description,
          capacityUnit: product.capacityUnit,
          stock: product.stock->Big.toFloat,
          packaging: product.packaging->Option.map(Big.toFloat),
          quantity: product.quantity->Big.toFloat,
          expectedQuantity: product.expectedQuantity->Big.toFloat,
          unitPrice: product.unitPrice->Js.Float.toString,
          taxes: product.taxes->Option.getExn->Array.map(Tax.serialize),
          fees: product.fees->Array.map(Fee.serialize),
          discounts: product.discounts->Array.map(discount =>
            discount->Discount.serialize(~capacityPrecision=precision)
          ),
        },
        precision,
      })
    }
}

module Cart = {
  // Retrieve Accounting.cartInput from serialized cartInput
  let deserializeInput = (cartInput: SerializedTypes.cartInput): cartInput => {
    products: cartInput.products->Array.map(Product.deserialize(~cart=cartInput)),
    discounts: cartInput.discounts->Array.map(Discount.deserialize),
    decimalPrecision: cartInput.decimalPrecision,
    currency: cartInput.currency->Currency.deserialize,
    taxesFree: cartInput.taxesFree,
    standardTaxRate: cartInput.standardTaxRate,
  }

  // Accounting.cart -> cartInput that can be serialized
  let serialize = (cart: cart): SerializedTypes.cartInput => {
    products: cart.products->Array.map(Product.serialize),
    discounts: cart.discounts->Array.map(discount => discount->Discount.serialize),
    decimalPrecision: cart.decimalPrecision,
    currency: cart.currency->Currency.serialize,
    taxesFree: cart.taxesFree,
    standardTaxRate: cart.standardTaxRate,
  }
}

// Adds missing fields to parsed cart to have a valid cartInput
let normalize: json => SerializedTypes.cartInput = %raw(`
    (cartInput) => {
      if (!cartInput.hasOwnProperty('decimalPrecision')) cartInput.decimalPrecision = 5
      if (!cartInput.hasOwnProperty('taxesFree')) cartInput.taxesFree = false
      if (!cartInput.hasOwnProperty('standardTaxRate')) cartInput.standardTaxRate = 20

      let products = cartInput.products.map(product => {
        if (
            (!product.hasOwnProperty('bulk') && product.hasOwnProperty('id')) ||
            (product.hasOwnProperty('bulk') && product.bulk !== true)
          ) {
          delete product.capacityPrecision
          delete product.bulk

          return {
            TAG: 0,
            product: product
          }
        } else if (product.bulk === true) {
          let capacityPrecision = product.capacityPrecision
          let pow = Math.pow(10, capacityPrecision)
          let mutatedProduct = {
            ...product,
            quantity: product.quantity / pow,
            expectedQuantity: product.expectedQuantity / pow,
            stock: product.stock / pow,
            packaging: product.packaging
              ? product.packaging / pow
              : undefined
          }

          delete product.capacityPrecision
          delete product.bulk

          return {
            TAG: 1,
            product: mutatedProduct,
            precision: capacityPrecision
          }
        }

        return product
      })

      return {
        ...cartInput,
        products: products
      }
    }
  `)

// Entry point of serialization
let serialize = (cart: cart): string => cart->Cart.serialize->stringify

// Entry point of deserialization
let deserialize = (stringified: string): cart => {
  let cartInput = stringified->parse->normalize->Cart.deserializeInput

  cartInput->Maker.Cart.make->Computer.make->Formatter.make
}
