open Accounting__Types

module Utils = Accounting__Utils
module Validator = Accounting__Validator

// NOTICE DO NOT COMPUTE any field value in the Maker, except .id and the final cart

module Fee = {
  // Given a feeInput, shape a fee
  // that can be used in intern
  let make = (input: feeInput): fee => {
    let fee = {
      // Input fields
      kind: input.kind,
      amount: input.amount,
      // Computed fields
      id: switch input.id {
      | Some(id) => id
      | None => Utils.makeUUID()
      },
      formattedAmount: None,
      totalAmount: None,
      formattedTotalAmount: None,
    }

    switch fee->Validator.Fee.validate() {
    | Ok() => fee
    | Error(error) => raise(error)
    }
  }

  let makeTransportFeePerUnit = (~globalFeeAmount, ~productsTotalUnits): fee => {
    let amount = if productsTotalUnits !== 0. {
      Big.fromFloat(globalFeeAmount)->Big.div(Big.fromFloat(productsTotalUnits))
    } else {
      Big.fromFloat(0.)
    }
    let input = {id: None, kind: Transport, amount: Big.toFloat(amount)}
    make(input)
  }

  let makeTransportFeeProratedByPrice = (
    ~globalFeeAmount,
    ~productQuantity,
    ~totalProductPriceExcludingDiscount,
    ~totalCartPriceExcludingGlobalDiscount,
  ): fee => {
    let amount = if totalCartPriceExcludingGlobalDiscount !== 0. && productQuantity !== 0. {
      Big.fromFloat(totalProductPriceExcludingDiscount)
      ->Big.div(Big.fromFloat(totalCartPriceExcludingGlobalDiscount))
      ->Big.times(Big.fromFloat(globalFeeAmount))
      ->Big.div(Big.fromFloat(productQuantity))
    } else {
      Big.fromFloat(0.)
    }
    let input = {id: None, kind: Transport, amount: Big.toFloat(amount)}
    make(input)
  }
}

module Discount = {
  // Given a discountInput, shape a
  // discount that can be used in intern
  let make = (~capacityPrecision: option<int>=?, input: discountInput): discount => {
    let discount = {
      // Input fields
      name: input.name,
      kind: input.kind,
      value: input.value,
      quantity: input.quantity->Utils.fromRawProductQuantity(~capacityPrecision?),
      // Computed fields
      id: switch input.id {
      | Some(id) => id
      | None => Utils.makeUUID()
      },
      amount: None,
      formattedValue: None,
      formattedAmount: None,
      warnings: [],
    }

    switch discount->Validator.Discount.validate() {
    | Ok() => discount
    | Error(error) => raise(error)
    }
  }
}

module Product = {
  // Base on a productInput to shape
  // a usable product in intern
  let make = (input: productInput): product => {
    let product: product = switch input {
    | Unit({product: input}) =>
      Unit({
        // Input fields
        identifier: input.identifier,
        stockKeepingUnit: input.stockKeepingUnit,
        name: input.name,
        description: input.description,
        unitPrice: input.unitPrice,
        stock: input.stock,
        expectedQuantity: switch input.expectedQuantity {
        | Some(value) => value
        | None => input.quantity
        },
        expectedQuantityWarning: [],
        packaging: input.packaging,
        quantity: input.quantity,
        fees: input.fees->Array.map(Fee.make),
        discounts: input.discounts->Array.map(discount => discount->Discount.make),
        capacityUnit: input.capacityUnit,
        // Computed fields
        id: switch input.id {
        | Some(id) => id
        | None => Utils.makeUUID()
        },
        // Recalculated fields without global discounts
        availablesFeeKinds: None,
        totalPrice: None,
        totalLocalDiscounts: None,
        unitFee: None,
        totalFees: None,
        totalAmountExcludingGlobalDiscounts: None,
        // With global discounts
        totalGlobalDiscounts: None,
        totalDiscounts: None,
        totalTaxesExcludingGlobalDiscount: None,
        totalTaxes: None,
        taxes: Some([
          {
            // Input fields
            rate: input.tax,
            // Computed fields
            amount: None,
            formattedAmount: None,
          },
        ]),
        totalAmountExcludingTaxes: None,
        totalAmountIncludingTaxes: None,
        unitCost: None,
        // Formatted fields
        formattedStock: None,
        formattedQuantity: None,
        formattedExpectedQuantity: None,
        formattedUnitPrice: None,
        formattedTotalPrice: None,
        formattedUnitFee: None,
        formattedTotalFees: None,
        formattedTotalLocalDiscounts: None,
        formattedTotalDiscounts: None,
        formattedTotalAmountExcludingGlobalDiscounts: None,
        formattedTotalAmountIncludingTaxes: None,
        formattedTotalAmountExcludingTaxes: None,
      })
    | Bulk({product: input, precision}) =>
      Bulk(
        {
          // Input fields
          identifier: input.identifier,
          stockKeepingUnit: input.stockKeepingUnit,
          name: input.name,
          description: input.description,
          unitPrice: input.unitPrice,
          stock: input.stock->Big.fromFloat,
          expectedQuantity: switch input.expectedQuantity {
          | Some(value) => value
          | None => input.quantity
          }->Big.fromFloat,
          expectedQuantityWarning: [],
          packaging: input.packaging->Option.map(Big.fromFloat),
          quantity: input.quantity->Big.fromFloat,
          fees: input.fees->Array.map(Fee.make),
          discounts: input.discounts->Array.map(discount =>
            discount->Discount.make(~capacityPrecision=precision)
          ),
          capacityUnit: input.capacityUnit,
          // Computed fields
          id: switch input.id {
          | Some(id) => id
          | None => Utils.makeUUID()
          },
          // Recalculated fields without global discounts
          availablesFeeKinds: None,
          totalPrice: None,
          totalLocalDiscounts: None,
          unitFee: None,
          totalFees: None,
          totalAmountExcludingGlobalDiscounts: None,
          // With global discounts
          totalGlobalDiscounts: None,
          totalDiscounts: None,
          totalTaxesExcludingGlobalDiscount: None,
          totalTaxes: None,
          taxes: Some([
            {
              // Input fields
              rate: input.tax,
              // Computed fields
              amount: None,
              formattedAmount: None,
            },
          ]),
          totalAmountExcludingTaxes: None,
          totalAmountIncludingTaxes: None,
          unitCost: None,
          // Formatted fields
          formattedStock: None,
          formattedQuantity: None,
          formattedExpectedQuantity: None,
          formattedUnitPrice: None,
          formattedTotalPrice: None,
          formattedUnitFee: None,
          formattedTotalFees: None,
          formattedTotalLocalDiscounts: None,
          formattedTotalDiscounts: None,
          formattedTotalAmountExcludingGlobalDiscounts: None,
          formattedTotalAmountIncludingTaxes: None,
          formattedTotalAmountExcludingTaxes: None,
        },
        precision,
      )
    }

    switch product->Validator.Product.validate() {
    | Ok() => product
    | Error(error) => raise(error)
    }
  }
}

module Cart = {
  // To shape a trustable
  // cart we need a cartInput
  let make = (input: cartInput): cart =>
    switch input->Validator.Cart.validateInput() {
    | Ok() => {
        // Input fields
        products: input.products->Array.map(Product.make),
        discounts: input.discounts->Array.map(discount => discount->Discount.make),
        currency: input.currency,
        decimalPrecision: input.decimalPrecision,
        taxesFree: input.taxesFree,
        standardTaxRate: input.standardTaxRate,
        // Recalculated fields
        fees: None,
        totalAmountOfGoods: None,
        totalAmountExcludingGlobalDiscounts: None,
        taxes: None,
        totalTaxes: None,
        totalAmountIncludingTaxes: None,
        totalAmountExcludingTaxes: None,
        totalProductsExpectedQuantity: None,
        totalProductsQuantity: None,
        totalDiscounts: None,
        formattedTotalTaxes: None,
        formattedTotalAmountOfGoods: None,
        formattedTotalAmountExcludingGlobalDiscounts: None,
        formattedTotalAmountIncludingTaxes: None,
        formattedTotalAmountExcludingTaxes: None,
      }
    | Error(error) => raise(error)
    }
}
