open Accounting__Types

module Utils = Accounting__Utils

// Converts a postfix to label
let postfixToString = (postfix: postfix) =>
  switch postfix {
  | Currency(currency) =>
    switch currency {
    | Eur => `€`
    | Usd => `$`
    | Pound => `£`
    }
  | Percent => "%"
  }

// Given a Big value, this function formats it
// to a readable string with its postfix and the capacityUnit if bulk
// We need to ask for precision here because the round()
// method is not padding value with zero leading
let formatAmountFromBig = (
  ~currency: currency,
  ~bulkUnit: option<string>=?,
  ~precision: option<int>=?,
  value: Big.t,
) => {
  let value = switch precision {
  | Some(precision) => value->Big.toFixed(precision)
  | None => value->Big.valueOf
  }

  let bulkSuffix = switch bulkUnit {
  | Some(unit) => `/${unit}`
  | None => ``
  }

  let amount = switch currency {
  | Usd => `${Currency(currency)->postfixToString}${value}`
  | _ => `${value} ${Currency(currency)->postfixToString}`
  }

  `${amount}${bulkSuffix}`
}

let formatAmount = (
  ~currency: currency,
  ~bulkUnit: option<string>=?,
  ~precision: option<int>=?,
  value: float,
) => formatAmountFromBig(Big.fromFloat(value), ~currency, ~bulkUnit?, ~precision?)

let formatAmountFromString = (
  ~currency: currency,
  ~bulkUnit: option<string>=?,
  ~precision: option<int>=?,
  value: string,
) => formatAmountFromBig(Big.fromString(value), ~currency, ~bulkUnit?, ~precision?)

// Given a stock value (Big.t), this function formats it
// to a readable string with its postfix and the capacityUnit if bulk
let formatQuantityFromBig = (
  ~bulkUnit: option<string>=?,
  ~precision: option<int>=?,
  value: Big.t,
) => {
  let value = switch precision {
  | Some(precision) => value->Big.toFixed(precision)
  | _ => value->Big.valueOf
  }

  switch bulkUnit {
  | Some(unit) => `${value} ${unit}`
  | _ => value
  }
}

let formatQuantity = (~bulkUnit: option<string>=?, ~precision: option<int>=?, value: float) =>
  formatQuantityFromBig(Big.fromFloat(value), ~bulkUnit?, ~precision?)

let formatQuantityFromString = (
  ~bulkUnit: option<string>=?,
  ~precision: option<int>=?,
  value: string,
) => formatQuantityFromBig(Big.fromString(value), ~bulkUnit?, ~precision?)

// Format a float value with percentage
let formatPercent = value => `${value->Big.valueOf} ${Percent->postfixToString}`

module Fee = {
  let make = (fee: fee, ~product: product, ~currency: currency, ~precision: option<int>, ()) => {
    let capacityUnit = switch product {
    | Unit({capacityUnit})
    | Bulk({capacityUnit}, _) => capacityUnit
    }

    {
      ...fee,
      formattedAmount: Some(
        formatAmount(
          ~currency,
          ~bulkUnit=?product->Utils.isBulk ? capacityUnit : None,
          ~precision?,
          fee.amount,
        ),
      ),
      formattedTotalAmount: switch fee.totalAmount {
      | None => None
      | Some(amount) =>
        Some(
          formatAmountFromBig(
            amount,
            ~currency,
            ~bulkUnit=?product->Utils.isBulk ? capacityUnit : None,
            ~precision?,
          ),
        )
      },
    }
  }
}

module Discount = {
  let make = (discount: discount, ~currency: currency, ~precision: option<int>) => {
    ...discount,
    formattedAmount: switch discount.amount {
    | None => None
    | Some(amount) => Some(formatAmountFromBig(amount, ~currency, ~precision?))
    },
    formattedValue: switch discount.kind {
    | Percent => Some(discount.value->Big.fromFloat->formatPercent)
    | Currency => Some(formatAmount(discount.value, ~precision?, ~currency))
    | Free => None
    },
  }
}

module ProductTax = {
  let make = (productTax: tax, ~currency: currency, ~precision: option<int>) => {
    ...productTax,
    formattedAmount: switch productTax.amount {
    | Some(amount) => Some(formatAmountFromBig(amount, ~precision?, ~currency))
    | None => None
    },
  }
}

module CartFee = {
  let make = (fee: feesTotal, ~currency: currency, ~precision: option<int>) => {
    ...fee,
    formattedAmount: Some(formatAmountFromBig(fee.amount, ~precision?, ~currency)),
  }
}

module CartTax = {
  let make = (cartTax: tax, ~currency: currency, ~precision: option<int>) => {
    ...cartTax,
    formattedAmount: switch cartTax.amount {
    | Some(amount) => Some(formatAmountFromBig(amount, ~precision?, ~currency))
    | None => None
    },
  }
}

module Product = {
  let make = (product: product, ~currency: currency, ~precision: option<int>) => {
    let fees = switch product {
    | Unit({fees}) | Bulk({fees}, _) =>
      fees->Array.map(fee => fee->Fee.make(~product, ~currency, ~precision, ()))
    }

    switch product {
    | Unit(product) =>
      let stock = product.stock->Float.fromInt->Big.fromFloat
      let quantity = product.quantity->Float.fromInt->Big.fromFloat
      let expectedQuantity = product.expectedQuantity->Float.fromInt->Big.fromFloat

      Unit({
        ...product,
        fees,
        discounts: product.discounts->Array.map(Discount.make(~currency, ~precision)),
        taxes: switch product.taxes {
        | None => None
        | Some(taxes) => Some(taxes->Array.map(ProductTax.make(~currency, ~precision)))
        },
        formattedStock: Some(formatQuantityFromBig(stock)),
        formattedQuantity: Some(formatQuantityFromBig(quantity)),
        formattedExpectedQuantity: Some(formatQuantityFromBig(expectedQuantity)),
        formattedUnitPrice: Some(formatAmount(product.unitPrice, ~precision?, ~currency)),
        formattedTotalPrice: switch product.totalPrice {
        | None => None
        | Some(totalPrice) => Some(formatAmountFromBig(totalPrice, ~currency, ~precision?))
        },
        formattedUnitFee: switch product.unitFee {
        | None => None
        | Some(unitFee) => Some(formatAmountFromBig(unitFee, ~currency, ~precision?))
        },
        formattedTotalFees: switch product.totalFees {
        | None => None
        | Some(totalFees) => Some(formatAmountFromBig(totalFees, ~currency, ~precision?))
        },
        formattedTotalLocalDiscounts: switch product.totalLocalDiscounts {
        | None => None
        | Some(totalLocalDiscounts) =>
          Some(formatAmountFromBig(totalLocalDiscounts, ~currency, ~precision?))
        },
        formattedTotalDiscounts: switch product.totalDiscounts {
        | Some(totalDiscounts) => Some(formatAmountFromBig(totalDiscounts, ~currency, ~precision?))
        | None => None
        },
        formattedTotalAmountExcludingGlobalDiscounts: switch product.totalAmountExcludingGlobalDiscounts {
        | Some(totalAmountExcludingGlobalDiscounts) =>
          Some(formatAmountFromBig(totalAmountExcludingGlobalDiscounts, ~currency, ~precision?))
        | None => None
        },
        formattedTotalAmountExcludingTaxes: switch product.totalAmountExcludingTaxes {
        | Some(totalAmountExcludingTaxes) =>
          Some(formatAmountFromBig(totalAmountExcludingTaxes, ~currency, ~precision?))
        | None => None
        },
        formattedTotalAmountIncludingTaxes: switch product.totalAmountIncludingTaxes {
        | Some(totalAmountIncludingTaxes) =>
          Some(formatAmountFromBig(totalAmountIncludingTaxes, ~currency, ~precision?))
        | None => None
        },
      })
    | Bulk(product, capacityPrecision) =>
      Bulk(
        {
          ...product,
          fees,
          discounts: product.discounts->Array.map(Discount.make(~currency, ~precision)),
          taxes: switch product.taxes {
          | None => None
          | Some(taxes) => Some(taxes->Array.map(ProductTax.make(~currency, ~precision)))
          },
          formattedStock: Some(
            formatQuantityFromBig(
              product.stock,
              ~bulkUnit=?product.capacityUnit,
              ~precision=capacityPrecision,
            ),
          ),
          formattedQuantity: Some(
            formatQuantityFromBig(
              product.quantity,
              ~bulkUnit=?product.capacityUnit,
              ~precision=capacityPrecision,
            ),
          ),
          formattedExpectedQuantity: Some(
            formatQuantityFromBig(
              product.expectedQuantity,
              ~bulkUnit=?product.capacityUnit,
              ~precision=capacityPrecision,
            ),
          ),
          formattedUnitPrice: Some(
            formatAmount(
              product.unitPrice,
              ~precision?,
              ~bulkUnit=?product.capacityUnit,
              ~currency,
            ),
          ),
          formattedTotalPrice: switch product.totalPrice {
          | None => None
          | Some(totalPrice) =>
            Some(
              formatAmountFromBig(
                totalPrice,
                ~currency,
                ~precision?,
                ~bulkUnit=?product.capacityUnit,
              ),
            )
          },
          formattedUnitFee: switch product.unitFee {
          | None => None
          | Some(unitFee) =>
            Some(
              formatAmountFromBig(unitFee, ~currency, ~precision?, ~bulkUnit=?product.capacityUnit),
            )
          },
          formattedTotalFees: switch product.totalFees {
          | None => None
          | Some(totalFees) => Some(formatAmountFromBig(totalFees, ~currency, ~precision?))
          },
          formattedTotalLocalDiscounts: switch product.totalLocalDiscounts {
          | None => None
          | Some(totalLocalDiscounts) =>
            Some(formatAmountFromBig(totalLocalDiscounts, ~currency, ~precision?))
          },
          formattedTotalDiscounts: switch product.totalDiscounts {
          | Some(totalDiscounts) =>
            Some(formatAmountFromBig(totalDiscounts, ~currency, ~precision?))
          | None => None
          },
          formattedTotalAmountExcludingGlobalDiscounts: switch product.totalAmountExcludingGlobalDiscounts {
          | Some(totalAmountExcludingGlobalDiscounts) =>
            Some(formatAmountFromBig(totalAmountExcludingGlobalDiscounts, ~currency, ~precision?))
          | None => None
          },
          formattedTotalAmountExcludingTaxes: switch product.totalAmountExcludingTaxes {
          | Some(totalAmountExcludingTaxes) =>
            Some(formatAmountFromBig(totalAmountExcludingTaxes, ~currency, ~precision?))
          | None => None
          },
          formattedTotalAmountIncludingTaxes: switch product.totalAmountIncludingTaxes {
          | Some(totalAmountIncludingTaxes) =>
            Some(formatAmountFromBig(totalAmountIncludingTaxes, ~currency, ~precision?))
          | None => None
          },
        },
        capacityPrecision,
      )
    }
  }
}

module Cart = {
  let make = (cart: cart) => {
    ...cart,
    taxes: switch cart.taxes {
    | Some(taxes) =>
      Some(
        taxes->Array.map(
          CartTax.make(~currency=cart.currency, ~precision=Some(cart.decimalPrecision)),
        ),
      )
    | None => None
    },
    fees: switch cart.fees {
    | Some(fees) =>
      Some(
        fees->Array.map(
          CartFee.make(~currency=cart.currency, ~precision=Some(cart.decimalPrecision)),
        ),
      )
    | None => None
    },
    discounts: cart.discounts->Array.map(
      Discount.make(~currency=cart.currency, ~precision=Some(cart.decimalPrecision)),
    ),
    products: cart.products->Array.map(
      Product.make(~currency=cart.currency, ~precision=Some(cart.decimalPrecision)),
    ),
    formattedTotalTaxes: switch cart.totalTaxes {
    | Some(totalTaxes) =>
      Some(
        formatAmountFromBig(totalTaxes, ~currency=cart.currency, ~precision=cart.decimalPrecision),
      )
    | None => None
    },
    formattedTotalAmountOfGoods: switch cart.totalAmountOfGoods {
    | Some(totalAmountOfGoods) =>
      Some(
        formatAmountFromBig(
          totalAmountOfGoods,
          ~currency=cart.currency,
          ~precision=cart.decimalPrecision,
        ),
      )
    | None => None
    },
    formattedTotalAmountExcludingGlobalDiscounts: switch cart.totalAmountExcludingGlobalDiscounts {
    | Some(totalAmountExcludingGlobalDiscounts) =>
      Some(
        formatAmountFromBig(
          totalAmountExcludingGlobalDiscounts,
          ~currency=cart.currency,
          ~precision=cart.decimalPrecision,
        ),
      )
    | None => None
    },
    formattedTotalAmountExcludingTaxes: switch cart.totalAmountExcludingTaxes {
    | Some(totalAmountExcludingTaxes) =>
      Some(
        formatAmountFromBig(
          totalAmountExcludingTaxes,
          ~currency=cart.currency,
          ~precision=cart.decimalPrecision,
        ),
      )
    | None => None
    },
    formattedTotalAmountIncludingTaxes: switch cart.totalAmountIncludingTaxes {
    | Some(totalAmountIncludingTaxes) =>
      Some(
        formatAmountFromBig(
          totalAmountIncludingTaxes,
          ~currency=cart.currency,
          ~precision=cart.decimalPrecision,
        ),
      )
    | None => None
    },
  }
}

// Parses the whole cart and format
// each data needed to be formatted
let make = cart => cart->Cart.make
