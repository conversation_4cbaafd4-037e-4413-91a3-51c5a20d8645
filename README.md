# accounting

[![Actions Status](https://github.com/winoteam/accounting/workflows/accounting-test-suite/badge.svg)](https://github.com/winoteam/accounting/actions)

> The [`v3` branch](../../tree/v3-stable) is currently in the development phase of `wino@v1`.

💸 All the utilities necessary for managing the accounting.

## 📦 Installation

Using npm:

```bash
npm install @wino/accounting
```

Using yarn:

```bash
yarn add @wino/accounting
```

Now you can use `@wino/accounting` package to manage
the cashing process of your POS application.

## 💻 How to use

The module name is `Accounting`.

It can be opened this way:

```rescript
open Accounting
```

and used as follows:

```rescript
open Accounting;
open Accounting.Types;

let initialCart =
  {
    discounts: [],
    products: [],
    decimalPrecision: 3,
    currency: Eur,
    taxesIncluded: false,
  }
  ->Maker.Cart.make
  ->Computer.make
  ->Formatter.make
```

### Dev container

If you have [docker](https://www.docker.com/) installed,
you might use the dev container in order to start working
on that module without having to install nodejs on your machine.

```
sh/dev
```

Then, inside the dev container, you can:

1. Install the dependencies

```
sh/deps
```

1. Run the tests

```
sh/test
```

### 🚀 Deploy instructions

#### 🔬 Experimental version

Just run this command if you are about to test the package in other modules

```bash
yarn pub:xp
```

#### 🎖 Stable version

When you are sure your code is stable you can publish it by running:

```bash
yarn pub:stable <option>
```

Here option can be: `--patch`, `--minor` or `--major`

After running this command, bunch of processes will start, bundling Typescript and ReScript dedicated packages, commit all changes, push to NPM registry and push updates to `origin`
