# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: accounting-test-suite

on:
  pull_request:
    types: [ready_for_review]
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2

      - name: Setting up Node.js
        uses: actions/setup-node@v1
        with:
          node-version: 22

      - name: Install
        run: yarn

      - name: Build and bundle
        run: yarn build && yarn bundle

      - name: Test
        run: yarn test
