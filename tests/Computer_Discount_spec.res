open Jest
open Fixtures
open Accounting__Exception
open Accounting__Types

module Maker = Accounting__Maker
module ComputerDiscount = Accounting__Computer__Discount
module ComputerProduct = Accounting__Computer__Product
module Computer = Accounting__Computer

let shouldRaiseError = "Should raise an error"

describe("ComputeDiscount", () => {
  open Expect

  let initialCart = {
    products: [],
    discounts: [],
    decimalPrecision: 2,
    currency: Eur,
    taxesFree: false,
    standardTaxRate: 20.,
  }->Maker.Cart.make

  let quantity_13_unit_price_20_11_tax_10 = {
    open ProductInputs
    make(
      quantity_13_unit_price_20_11_tax_10,
      ~discounts=[DiscountInputs.percent_20],
      ~fees=[FeeInputs.transport_1_09, FeeInputs.tax_0_09],
      (),
    )
  }
  let quantity_3_unit_price_1_33_tax_20 = {
    open ProductInputs
    make(
      quantity_3_unit_price_1_33_tax_20,
      ~discounts=[DiscountInputs.currency_0_33],
      ~fees=[FeeInputs.transport_6_66],
      (),
    )
  }
  let quantity_0_unit_price_20_1281_tax_20 = {
    open ProductInputs
    make(
      quantity_0_unit_price_20_1281_tax_20,
      ~discounts=[],
      ~fees=[FeeInputs.transport_1_09, FeeInputs.other_0_01],
      (),
    )
  }
  let cart =
    {
      products: [
        quantity_13_unit_price_20_11_tax_10,
        quantity_3_unit_price_1_33_tax_20,
        quantity_0_unit_price_20_1281_tax_20,
      ],
      discounts: [DiscountInputs.currency_0_01],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }
    ->Maker.Cart.make
    ->Computer.make

  describe("Global", () => {
    open ComputerDiscount.Global

    describe(
      "computePercent",
      () => {
        let discount = DiscountInputs.percent_3_33->Maker.Discount.make

        test(
          "should compute percent global discount amount",
          () => expect(discount->computePercent(~cart)) |> toEqual(Some(7.09->Big.fromFloat)),
        )
        test(
          "should match snapshot",
          () => expect(discount->computePercent(~cart)) |> toMatchSnapshot,
        )
      },
    )

    describe(
      "computeCurrency",
      () => {
        let discount = DiscountInputs.currency_0_01->Maker.Discount.make

        test(
          "should compute global currency discount amount",
          () => expect(discount->computeCurrency(~cart)) |> toEqual(Some(0.01->Big.fromFloat)),
        )
        test(
          "should match snapshot",
          () => expect(discount->computeCurrency(~cart)) |> toMatchSnapshot,
        )
      },
    )

    describe(
      "computeFree",
      () => {
        let discount = DiscountInputs.free_3->Maker.Discount.make

        test(
          "should not be able to apply global free discount",
          () =>
            switch discount->computeFree {
            | exception NotPermitted(message) => expect(message) |> toMatchSnapshot
            | _ => fail(shouldRaiseError)
            },
        )
      },
    )

    describe(
      "compute",
      () => {
        test(
          "should compute global percent discount amount through compute",
          () => {
            // The discount is a percent discount
            // So, compute and computePercent should output same result
            let discount = DiscountInputs.percent_20->Maker.Discount.make

            expect(discount->compute(~cart)) |> toEqual(discount->computePercent(~cart))
          },
        )

        test(
          "should compute global currency discount amount through compute",
          () => {
            // The discount is a currency discount
            // So, compute and computeCurrency should output same result
            let discount = DiscountInputs.currency_6_66->Maker.Discount.make

            expect(discount->compute(~cart)) |> toEqual(discount->computeCurrency(~cart))
          },
        )

        test(
          "should not be able to compute global free discount amount through compute",
          () => {
            // The discount is a free discount
            // So, compute should raise an error
            let discount = DiscountInputs.free_0->Maker.Discount.make

            switch discount->compute(~cart) {
            | exception NotPermitted(message) => expect(message) |> toMatchSnapshot
            | _ => fail(shouldRaiseError)
            }
          },
        )
      },
    )
  })

  describe("Local", () => {
    open ComputerDiscount.Local

    let product =
      quantity_13_unit_price_20_11_tax_10
      ->Maker.Product.make
      ->ComputerProduct.preCompute(~cart=initialCart)
    let capacityPrecision = switch product {
    | Bulk(_, precision) => Some(precision)
    | Unit(_) => None
    }

    describe(
      "computePercent",
      () => {
        let discount = DiscountInputs.percent_3_33->Maker.Discount.make

        test(
          "should compute percent global discount amount",
          () =>
            expect(discount->computePercent(~product, ~cart)) |> toEqual(Some(8.71->Big.fromFloat)),
        )
        test(
          "should match snapshot",
          () => expect(discount->computePercent(~product, ~cart=initialCart)) |> toMatchSnapshot,
        )
      },
    )

    describe(
      "computeCurrency",
      () => {
        let discount = DiscountInputs.currency_0_01->Maker.Discount.make(~capacityPrecision?)

        test(
          "should compute global currency discount amount",
          () =>
            expect(discount->computeCurrency(~product, ~cart=initialCart)) |> toEqual(
              Some(0.01->Big.fromFloat),
            ),
        )
        test(
          "should match snapshot",
          () => expect(discount->computeCurrency(~product, ~cart=initialCart)) |> toMatchSnapshot,
        )
      },
    )

    describe(
      "computeFree",
      () => {
        let discount = DiscountInputs.free_3->Maker.Discount.make(~capacityPrecision?)

        test(
          "should compute local free discount",
          () =>
            expect(discount->computeFree(~product, ~cart=initialCart)) |> toEqual(
              Some(60.33->Big.fromFloat),
            ),
        )
      },
    )

    describe(
      "compute",
      () => {
        test(
          "should compute local percent discount amount through compute",
          () => {
            // The discount is a percent discount
            // So, compute and computePercent should output same result
            let discount = DiscountInputs.percent_20->Maker.Discount.make(~capacityPrecision?)

            expect(discount->compute(~product, ~cart=initialCart)) |> toEqual(
              discount->computePercent(~product, ~cart=initialCart),
            )
          },
        )

        test(
          "should compute local currency discount amount through compute",
          () => {
            // The discount is a currency discount
            // So, compute and computeCurrency should output same result
            let discount = DiscountInputs.currency_6_66->Maker.Discount.make(~capacityPrecision?)

            expect(discount->compute(~product, ~cart=initialCart)) |> toEqual(
              discount->computeCurrency(~product, ~cart=initialCart),
            )
          },
        )

        test(
          "should compute local free discount amount through compute",
          () => {
            // The discount is a free discount
            // So, compute should output same as computeFree
            let discount = DiscountInputs.free_0->Maker.Discount.make(~capacityPrecision?)

            expect(discount->compute(~product, ~cart=initialCart)) |> toEqual(
              discount->computeFree(~product, ~cart=initialCart),
            )
          },
        )
      },
    )
  })
})
