open Jest
open Fixtures
open Accounting__Types
open Accounting__Exception

module Maker = Accounting__Maker
module Utils = Accounting__Utils
module Computer = Accounting__Computer

describe("Utils", () => {
  open Expect
  open Utils

  let productA = {
    open ProductInputs
    make(
      quantity_10_unit_price_10_01_tax_20,
      ~discounts=[DiscountInputs.percent_20],
      ~fees=[FeeInputs.other_6_66],
      (),
    )
  }
  let productB = {
    open ProductInputs
    make(
      quantity_1_unit_price_0_09_tax_5_5,
      ~discounts=[DiscountInputs.free_0],
      ~fees=[FeeInputs.tax_0_09, FeeInputs.transport_1_09],
      (),
    )
  }
  let productC = {
    open ProductInputs
    make(quantity_3_unit_price_0_19_tax_2_1, ~fees=[FeeInputs.tax_3_33], ())
  }
  let productD = {
    open ProductInputs
    make(quantity_0_unit_price_20_1281_tax_20, ~discounts=[DiscountInputs.currency_6_66], ())
  }

  let (productIdentifierA, productIdentifierB, productIdentifierD) = switch (
    productA,
    productB,
    productD,
  ) {
  | (
      Unit({product: {identifier: a}}) | Bulk({product: {identifier: a}, _}),
      Unit({product: {identifier: b}}) | Bulk({product: {identifier: b}, _}),
      Unit({product: {identifier: d}}) | Bulk({product: {identifier: d}, _}),
    ) => (a, b, d)
  }

  let cart =
    {
      products: [productA],
      discounts: [],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }
    ->Maker.Cart.make
    ->Computer.make

  describe("getAllFeeKinds", () =>
    test(
      "should return all fee kinds",
      () =>
        // Seriously ?
        expect(getAllFeeKinds()) |> toEqual([Transport, Taxes, Other]),
    )
  )

  describe("canAcceptFeeKind", () => {
    describe(
      "with errors",
      () =>
        test(
          "should not be able to accept this fee kind",
          () => {
            let product =
              ProductInputs.make(
                ProductInputs.quantity_3_unit_price_0_19_tax_2_1,
                ~fees=[FeeInputs.transport_0_93],
                (),
              )->Maker.Product.make

            expect(product->canAcceptFeeKind(~feeKind=Transport)) |> toBe(false)
          },
        ),
    )

    describe(
      "without errors",
      () =>
        test(
          "should be able to accept this fee kind",
          () =>
            expect(
              Products.quantity_3_unit_price_0_19_tax_2_1->canAcceptFeeKind(~feeKind=Transport),
            ) |> toBe(true),
        ),
    )
  })

  describe("getAvailableFeeKinds", () =>
    test(
      "should return available fee kinds",
      () => {
        let expectedResults: (array<feeKind>, array<feeKind>, array<feeKind>, array<feeKind>) = (
          [Transport, Taxes],
          [Other],
          [Transport, Other],
          [Transport, Taxes, Other],
        )

        expect((
          productA->Maker.Product.make->getAvailableFeeKinds,
          productB->Maker.Product.make->getAvailableFeeKinds,
          productC->Maker.Product.make->getAvailableFeeKinds,
          productD->Maker.Product.make->getAvailableFeeKinds,
        )) |> toEqual(expectedResults)
      },
    )
  )

  describe("productIsIdentifiedBy", () =>
    test(
      "should output correct values",
      () =>
        expect((
          productA->Maker.Product.make->productIsIdentifiedBy(~key=Some("")),
          productB->Maker.Product.make->productIsIdentifiedBy(~key=productIdentifierB),
          productD->Maker.Product.make->productIsIdentifiedBy(~key=productIdentifierD),
          productC->Maker.Product.make->productIsIdentifiedBy(~key=productIdentifierD),
        )) |> toEqual((false, true, true, false)),
    )
  )

  describe("cartHasProduct", () =>
    test(
      "should check product presence in cart",
      () => {
        let productIdD = switch productD->Maker.Product.make {
        | Unit({id}) | Bulk({id}, _) => id
        }

        expect((
          cart->cartHasProduct(productIdentifierB),
          cart->cartHasProduct(Some(productIdD)),
          cart->cartHasProduct(productIdentifierA),
        )) |> toEqual((false, false, true))
      },
    )
  )

  describe("getProductByKey", () => {
    test(
      "could not retreive product by key",
      () =>
        switch cart.products->getProductByKey(productIdentifierB) {
        | exception NotFound(message) => expect(message) |> toMatchSnapshot
        | _ => fail("Should not find the product in the cart")
        },
    )
    test(
      "should retreive product by its id",
      () => {
        let unitPrice = switch productA {
        | Unit({product: {unitPrice}}) | Bulk({product: {unitPrice}, _}) => unitPrice
        }
        let foundUnitPrice = switch cart.products->getProductByKey(productIdentifierA) {
        | Unit({unitPrice}) | Bulk({unitPrice}, _) => unitPrice
        }

        expect(foundUnitPrice) |> toBe(unitPrice)
      },
    )
  })

  describe("isMultipleOf", () =>
    test(
      "if a decimal value is a multiple of some other decimal",
      () => {
        let quantity = 18.->Big.fromFloat
        let packaging = 6.->Big.fromFloat

        (expect(quantity->Utils.isMultipleOf(packaging)) |> toBe(true))->ignore

        let quantity = 25.->Big.fromFloat
        let packaging = 12.5->Big.fromFloat

        (expect(quantity->Utils.isMultipleOf(packaging)) |> toBe(true))->ignore

        let quantity = 11.->Big.fromFloat
        let packaging = 5.->Big.fromFloat

        expect(quantity->Utils.isMultipleOf(packaging)) |> toBe(false)
      },
    )
  )

  describe("fromRawProductQuantity", () =>
    test(
      "if it converts the integer to a decimal value based on precision",
      () => {
        let input = 3000->fromRawProductQuantity(~capacityPrecision=3)

        (expect(input) |> toEqual(3.->Big.fromFloat))->ignore

        let input = 300->fromRawProductQuantity(~capacityPrecision=3)

        (expect(input) |> toEqual(0.3->Big.fromFloat))->ignore

        let input = 30->fromRawProductQuantity(~capacityPrecision=0)

        (expect(input) |> toEqual(30.->Big.fromFloat))->ignore

        let input = 30->fromRawProductQuantity

        expect(input) |> toEqual(30.->Big.fromFloat)
      },
    )
  )

  describe("toRawProductQuantity", () =>
    test(
      "if it converts the decimal to an integer value based on precision",
      () => {
        let input = 3.->Big.fromFloat->toRawProductQuantity(~capacityPrecision=3)

        (expect(input) |> toEqual(3000))->ignore

        let input = 0.3->Big.fromFloat->toRawProductQuantity(~capacityPrecision=3)

        (expect(input) |> toEqual(300))->ignore

        let input = 30.->Big.fromFloat->toRawProductQuantity(~capacityPrecision=0)

        (expect(input) |> toEqual(30))->ignore

        let input = 30.->Big.fromFloat->toRawProductQuantity

        expect(input) |> toEqual(30)
      },
    )
  )
})
