// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Validation Cart validateInput with errors should not be a valid cart input - error in discounts 1`] = `"Discount currency value must be >= 0"`;

exports[`Validation Cart validateInput with errors should not be a valid cart input - error in products 1`] = `"Product quantity must be >= 0"`;

exports[`Validation Cart validateInput with errors should not be a valid cart input - more than one discount 1`] = `"Cart must not have more than one discount"`;

exports[`Validation Cart validateInput without errors should be a valid cart input 1`] = `
Object {
  "TAG": 0,
  "_0": undefined,
}
`;

exports[`Validation Discount validate with errors should not validate discount as a good one - currency and negative value 1`] = `"Discount currency value must be >= 0"`;

exports[`Validation Discount validate with errors should not validate discount as a good one - free and negative value 1`] = `"Discount quantity must be >= 0"`;

exports[`Validation Discount validate with errors should not validate discount as a good one - percent and incorrect value 1`] = `"Discount percent value must be >= 0 and <= 100"`;

exports[`Validation Discount validate without errors should validate discount as a good one 1`] = `
Object {
  "TAG": 0,
  "_0": undefined,
}
`;

exports[`Validation Fee validate with errors should not validate fee as a good one 1`] = `"Fee amount must be >= 0"`;

exports[`Validation Fee validate with errors should not validate fee input as a good one 1`] = `"Fee amount must be >= 0"`;

exports[`Validation Fee validate without errors should validate fee as a good one 1`] = `
Object {
  "TAG": 0,
  "_0": undefined,
}
`;

exports[`Validation Fee validate without errors should validate fee input as a good one 1`] = `
Object {
  "TAG": 0,
  "_0": undefined,
}
`;

exports[`Validation Product validateInput with errors should not be a valid product - discount inside not valid 1`] = `"Discount percent value must be >= 0 and <= 100"`;

exports[`Validation Product validateInput with errors should not be a valid product - empty description 1`] = `"Product description cannot be empty"`;

exports[`Validation Product validateInput with errors should not be a valid product - empty name 1`] = `"Product name cannot be empty"`;

exports[`Validation Product validateInput with errors should not be a valid product - fee inside not valid 1`] = `"Fee amount must be >= 0"`;

exports[`Validation Product validateInput with errors should not be a valid product - many fees with same feeKind 1`] = `"Product should not have more than one fee with the same fee kind"`;

exports[`Validation Product validateInput with errors should not be a valid product - negative quantity 1`] = `"Product expected quantity must be >= 0"`;

exports[`Validation Product validateInput with errors should not be a valid product - quantity zero 1`] = `"Strict mode - Product expected quantity must be > 0"`;

exports[`Validation Product validateInput with errors should not be a valid product input - discount inside is not valid 1`] = `"Discount quantity must be >= 0"`;

exports[`Validation Product validateInput with errors should not be a valid product input - empty description 1`] = `"Product description cannot be empty"`;

exports[`Validation Product validateInput with errors should not be a valid product input - empty name 1`] = `"Product name cannot be empty"`;

exports[`Validation Product validateInput with errors should not be a valid product input - fee inside is not valid 1`] = `"Fee amount must be >= 0"`;

exports[`Validation Product validateInput with errors should not be a valid product input - many fees with same feeKind 1`] = `"Product should not have more than one fee with the same fee kind"`;

exports[`Validation Product validateInput with errors should not be a valid product input - more than one discount 1`] = `"Product must not have more than one discounts"`;

exports[`Validation Product validateInput with errors should not be a valid product input - negative quantity 1`] = `"Product quantity must be >= 0"`;

exports[`Validation Product validateInput with errors should not be a valid product input - negative tax 1`] = `"Product tax must be >= 0"`;

exports[`Validation Product validateInput with errors should not be a valid product input - negative unit price 1`] = `"Product unitPrice must >= 0"`;

exports[`Validation Product validateInput with errors should not be a valid product input - quantity is zero in strict mode 1`] = `"Strict mode - Product quantity must be > 0"`;

exports[`Validation Product validateInput without errors should be a valid product input 1`] = `
Object {
  "TAG": 0,
  "_0": undefined,
}
`;
