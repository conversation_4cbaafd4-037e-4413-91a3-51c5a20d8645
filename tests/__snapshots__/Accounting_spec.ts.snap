// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`API layer exposed methods compute if the cart products are correctly computed without converted products 1`] = `
Object {
  "currency": "Eur",
  "decimalPrecision": 3,
  "discounts": Array [],
  "fees": Array [],
  "formattedTotalAmountExcludingGlobalDiscounts": undefined,
  "formattedTotalAmountExcludingTaxes": undefined,
  "formattedTotalAmountIncludingTaxes": undefined,
  "formattedTotalAmountOfGoods": undefined,
  "formattedTotalTaxes": undefined,
  "products": Array [
    Object {
      "availablesFeeKinds": Array [
        "Transport",
        "Taxes",
        "Other",
      ],
      "bulk": false,
      "capacityPrecision": null,
      "capacityUnit": undefined,
      "description": "Description of product 8",
      "discounts": Array [],
      "expectedQuantity": 0,
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": undefined,
      "formattedQuantity": undefined,
      "formattedStock": undefined,
      "formattedTotalAmountExcludingGlobalDiscounts": undefined,
      "formattedTotalAmountExcludingTaxes": undefined,
      "formattedTotalAmountIncludingTaxes": undefined,
      "formattedTotalDiscounts": undefined,
      "formattedTotalFees": undefined,
      "formattedTotalLocalDiscounts": undefined,
      "formattedTotalPrice": undefined,
      "formattedUnitFee": undefined,
      "formattedUnitPrice": undefined,
      "id": "v4-uuid-21",
      "identifier": "v4-uuid-5",
      "name": "Product 8",
      "packaging": 1,
      "quantity": 0,
      "stock": -1,
      "stockKeepingUnit": "1479427200005",
      "taxes": Array [
        Object {
          "amount": "0",
          "formattedAmount": undefined,
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "0",
      "totalAmountExcludingTaxes": "0",
      "totalAmountIncludingTaxes": "0",
      "totalDiscounts": "0",
      "totalFees": "0",
      "totalGlobalDiscounts": "0",
      "totalLocalDiscounts": "0",
      "totalPrice": "0",
      "totalTaxes": "0",
      "totalTaxesExcludingGlobalDiscount": "0",
      "unitCost": undefined,
      "unitFee": "0",
      "unitPrice": 20.1281,
    },
    Object {
      "availablesFeeKinds": Array [
        "Transport",
        "Taxes",
        "Other",
      ],
      "bulk": true,
      "capacityPrecision": 3,
      "capacityUnit": "g",
      "description": "Description of product 16",
      "discounts": Array [],
      "expectedQuantity": "25",
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": undefined,
      "formattedQuantity": undefined,
      "formattedStock": undefined,
      "formattedTotalAmountExcludingGlobalDiscounts": undefined,
      "formattedTotalAmountExcludingTaxes": undefined,
      "formattedTotalAmountIncludingTaxes": undefined,
      "formattedTotalDiscounts": undefined,
      "formattedTotalFees": undefined,
      "formattedTotalLocalDiscounts": undefined,
      "formattedTotalPrice": undefined,
      "formattedUnitFee": undefined,
      "formattedUnitPrice": undefined,
      "id": "v4-uuid-22",
      "identifier": "v4-uuid-11",
      "name": "Product 16",
      "packaging": "1",
      "quantity": "25",
      "stock": "120",
      "stockKeepingUnit": "1479427200011",
      "taxes": Array [
        Object {
          "amount": "28.45",
          "formattedAmount": undefined,
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "142.25",
      "totalAmountExcludingTaxes": "142.25",
      "totalAmountIncludingTaxes": "170.7",
      "totalDiscounts": "0",
      "totalFees": "0",
      "totalGlobalDiscounts": "0",
      "totalLocalDiscounts": "0",
      "totalPrice": "142.25",
      "totalTaxes": "28.45",
      "totalTaxesExcludingGlobalDiscount": "28.45",
      "unitCost": "5.69",
      "unitFee": "0",
      "unitPrice": 5.69,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "28.45",
      "formattedAmount": undefined,
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "142.25",
  "totalAmountExcludingTaxes": "142.25",
  "totalAmountIncludingTaxes": "170.7",
  "totalAmountOfGoods": "142.25",
  "totalDiscounts": "0",
  "totalProductsExpectedQuantity": 1,
  "totalProductsQuantity": 1,
  "totalTaxes": "28.45",
}
`;

exports[`API layer exposed methods deserialize if the cart has been correctly stringified with converted products 1`] = `
Object {
  "currency": "Eur",
  "decimalPrecision": 3,
  "discounts": Array [],
  "fees": Array [],
  "formattedTotalAmountExcludingGlobalDiscounts": "142.250 €",
  "formattedTotalAmountExcludingTaxes": "142.250 €",
  "formattedTotalAmountIncludingTaxes": "170.700 €",
  "formattedTotalAmountOfGoods": "142.250 €",
  "formattedTotalTaxes": "28.450 €",
  "products": Array [
    Object {
      "availablesFeeKinds": Array [
        "Transport",
        "Taxes",
        "Other",
      ],
      "bulk": false,
      "capacityPrecision": null,
      "capacityUnit": undefined,
      "description": "Description of product 8",
      "discounts": Array [],
      "expectedQuantity": 0,
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": "0",
      "formattedQuantity": "0",
      "formattedStock": "-1",
      "formattedTotalAmountExcludingGlobalDiscounts": "0.000 €",
      "formattedTotalAmountExcludingTaxes": "0.000 €",
      "formattedTotalAmountIncludingTaxes": "0.000 €",
      "formattedTotalDiscounts": "0.000 €",
      "formattedTotalFees": "0.000 €",
      "formattedTotalLocalDiscounts": "0.000 €",
      "formattedTotalPrice": "0.000 €",
      "formattedUnitFee": "0.000 €",
      "formattedUnitPrice": "20.128 €",
      "id": "v4-uuid-21",
      "identifier": "v4-uuid-5",
      "name": "Product 8",
      "packaging": 1,
      "quantity": 0,
      "stock": -1,
      "stockKeepingUnit": "1479427200005",
      "taxes": Array [
        Object {
          "amount": "0",
          "formattedAmount": "0.000 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "0",
      "totalAmountExcludingTaxes": "0",
      "totalAmountIncludingTaxes": "0",
      "totalDiscounts": "0",
      "totalFees": "0",
      "totalGlobalDiscounts": "0",
      "totalLocalDiscounts": "0",
      "totalPrice": "0",
      "totalTaxes": "0",
      "totalTaxesExcludingGlobalDiscount": "0",
      "unitCost": undefined,
      "unitFee": "0",
      "unitPrice": 20.1281,
    },
    Object {
      "availablesFeeKinds": Array [
        "Transport",
        "Taxes",
        "Other",
      ],
      "bulk": true,
      "capacityPrecision": 3,
      "capacityUnit": "g",
      "description": "Description of product 16",
      "discounts": Array [],
      "expectedQuantity": "25",
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": "25.000 g",
      "formattedQuantity": "25.000 g",
      "formattedStock": "120.000 g",
      "formattedTotalAmountExcludingGlobalDiscounts": "142.250 €",
      "formattedTotalAmountExcludingTaxes": "142.250 €",
      "formattedTotalAmountIncludingTaxes": "170.700 €",
      "formattedTotalDiscounts": "0.000 €",
      "formattedTotalFees": "0.000 €",
      "formattedTotalLocalDiscounts": "0.000 €",
      "formattedTotalPrice": "142.250 €/g",
      "formattedUnitFee": "0.000 €/g",
      "formattedUnitPrice": "5.690 €/g",
      "id": "v4-uuid-22",
      "identifier": "v4-uuid-11",
      "name": "Product 16",
      "packaging": "1",
      "quantity": "25",
      "stock": "120",
      "stockKeepingUnit": "1479427200011",
      "taxes": Array [
        Object {
          "amount": "28.45",
          "formattedAmount": "28.450 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "142.25",
      "totalAmountExcludingTaxes": "142.25",
      "totalAmountIncludingTaxes": "170.7",
      "totalDiscounts": "0",
      "totalFees": "0",
      "totalGlobalDiscounts": "0",
      "totalLocalDiscounts": "0",
      "totalPrice": "142.25",
      "totalTaxes": "28.45",
      "totalTaxesExcludingGlobalDiscount": "28.45",
      "unitCost": "5.69",
      "unitFee": "0",
      "unitPrice": 5.69,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "28.45",
      "formattedAmount": "28.450 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "142.25",
  "totalAmountExcludingTaxes": "142.25",
  "totalAmountIncludingTaxes": "170.7",
  "totalAmountOfGoods": "142.25",
  "totalDiscounts": "0",
  "totalProductsExpectedQuantity": 1,
  "totalProductsQuantity": 1,
  "totalTaxes": "28.45",
}
`;

exports[`API layer exposed methods serialize if the cart has been correctly stringified with converted products 1`] = `"{\\"products\\":[{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-21\\",\\"identifier\\":\\"v4-uuid-5\\",\\"stockKeepingUnit\\":\\"1479427200005\\",\\"name\\":\\"Product 8\\",\\"description\\":\\"Description of product 8\\",\\"stock\\":-1,\\"packaging\\":1,\\"quantity\\":0,\\"expectedQuantity\\":0,\\"unitPrice\\":\\"20.1281\\",\\"fees\\":[],\\"discounts\\":[],\\"taxes\\":[{\\"rate\\":\\"20\\"}]}},{\\"TAG\\":1,\\"product\\":{\\"id\\":\\"v4-uuid-22\\",\\"identifier\\":\\"v4-uuid-11\\",\\"stockKeepingUnit\\":\\"1479427200011\\",\\"name\\":\\"Product 16\\",\\"description\\":\\"Description of product 16\\",\\"capacityUnit\\":\\"g\\",\\"stock\\":120,\\"packaging\\":1,\\"quantity\\":25,\\"expectedQuantity\\":25,\\"unitPrice\\":\\"5.69\\",\\"fees\\":[],\\"discounts\\":[],\\"taxes\\":[{\\"rate\\":\\"20\\"}]},\\"precision\\":3}],\\"discounts\\":[],\\"decimalPrecision\\":3,\\"currency\\":\\"eur\\",\\"taxesFree\\":false,\\"standardTaxRate\\":20}"`;

exports[`Converting private methods productFromGeneratedStructure if given a generated Product it converts it 1`] = `
Object {
  "availablesFeeKinds": undefined,
  "bulk": false,
  "capacityPrecision": null,
  "capacityUnit": undefined,
  "description": "Description of product 8",
  "discounts": Array [],
  "expectedQuantity": 0,
  "expectedQuantityWarning": Array [],
  "fees": Array [],
  "formattedExpectedQuantity": undefined,
  "formattedQuantity": undefined,
  "formattedStock": undefined,
  "formattedTotalAmountExcludingGlobalDiscounts": undefined,
  "formattedTotalAmountExcludingTaxes": undefined,
  "formattedTotalAmountIncludingTaxes": undefined,
  "formattedTotalDiscounts": undefined,
  "formattedTotalFees": undefined,
  "formattedTotalLocalDiscounts": undefined,
  "formattedTotalPrice": undefined,
  "formattedUnitFee": undefined,
  "formattedUnitPrice": undefined,
  "id": "v4-uuid-19",
  "identifier": "v4-uuid-5",
  "name": "Product 8",
  "packaging": 1,
  "quantity": 0,
  "stock": -1,
  "stockKeepingUnit": "1479427200005",
  "taxes": Array [
    Object {
      "amount": undefined,
      "formattedAmount": undefined,
      "rate": 20,
    },
  ],
  "totalAmountExcludingGlobalDiscounts": undefined,
  "totalAmountExcludingTaxes": undefined,
  "totalAmountIncludingTaxes": undefined,
  "totalDiscounts": undefined,
  "totalFees": undefined,
  "totalGlobalDiscounts": undefined,
  "totalLocalDiscounts": undefined,
  "totalPrice": undefined,
  "totalTaxes": undefined,
  "totalTaxesExcludingGlobalDiscount": undefined,
  "unitCost": undefined,
  "unitFee": undefined,
  "unitPrice": 20.1281,
}
`;

exports[`Converting private methods productFromGeneratedStructure if given a generated Product it converts it 2`] = `
Object {
  "availablesFeeKinds": undefined,
  "bulk": true,
  "capacityPrecision": 3,
  "capacityUnit": "g",
  "description": "Description of product 16",
  "discounts": Array [],
  "expectedQuantity": "25",
  "expectedQuantityWarning": Array [],
  "fees": Array [],
  "formattedExpectedQuantity": undefined,
  "formattedQuantity": undefined,
  "formattedStock": undefined,
  "formattedTotalAmountExcludingGlobalDiscounts": undefined,
  "formattedTotalAmountExcludingTaxes": undefined,
  "formattedTotalAmountIncludingTaxes": undefined,
  "formattedTotalDiscounts": undefined,
  "formattedTotalFees": undefined,
  "formattedTotalLocalDiscounts": undefined,
  "formattedTotalPrice": undefined,
  "formattedUnitFee": undefined,
  "formattedUnitPrice": undefined,
  "id": "v4-uuid-20",
  "identifier": "v4-uuid-11",
  "name": "Product 16",
  "packaging": "1",
  "quantity": "25",
  "stock": "120",
  "stockKeepingUnit": "1479427200011",
  "taxes": Array [
    Object {
      "amount": undefined,
      "formattedAmount": undefined,
      "rate": 20,
    },
  ],
  "totalAmountExcludingGlobalDiscounts": undefined,
  "totalAmountExcludingTaxes": undefined,
  "totalAmountIncludingTaxes": undefined,
  "totalDiscounts": undefined,
  "totalFees": undefined,
  "totalGlobalDiscounts": undefined,
  "totalLocalDiscounts": undefined,
  "totalPrice": undefined,
  "totalTaxes": undefined,
  "totalTaxesExcludingGlobalDiscount": undefined,
  "unitCost": undefined,
  "unitFee": undefined,
  "unitPrice": 5.69,
}
`;

exports[`Converting private methods productToGeneratedStructure if given a converted Product it converts it to its origin structure 1`] = `
Object {
  "tag": "Unit",
  "value": Object {
    "availablesFeeKinds": undefined,
    "bulk": false,
    "capacityPrecision": null,
    "capacityUnit": undefined,
    "description": "Description of product 8",
    "discounts": Array [],
    "expectedQuantity": 0,
    "expectedQuantityWarning": Array [],
    "fees": Array [],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-19",
    "identifier": "v4-uuid-5",
    "name": "Product 8",
    "packaging": 1,
    "quantity": 0,
    "stock": -1,
    "stockKeepingUnit": "1479427200005",
    "taxes": Array [
      Object {
        "amount": undefined,
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": undefined,
    "totalAmountExcludingTaxes": undefined,
    "totalAmountIncludingTaxes": undefined,
    "totalDiscounts": undefined,
    "totalFees": undefined,
    "totalGlobalDiscounts": undefined,
    "totalLocalDiscounts": undefined,
    "totalPrice": undefined,
    "totalTaxes": undefined,
    "totalTaxesExcludingGlobalDiscount": undefined,
    "unitCost": undefined,
    "unitFee": undefined,
    "unitPrice": 20.1281,
  },
}
`;

exports[`Converting private methods productToGeneratedStructure if given a converted Product it converts it to its origin structure 2`] = `
Object {
  "tag": "Bulk",
  "value": Array [
    Object {
      "availablesFeeKinds": undefined,
      "bulk": true,
      "capacityUnit": "g",
      "description": "Description of product 16",
      "discounts": Array [],
      "expectedQuantity": "25",
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": undefined,
      "formattedQuantity": undefined,
      "formattedStock": undefined,
      "formattedTotalAmountExcludingGlobalDiscounts": undefined,
      "formattedTotalAmountExcludingTaxes": undefined,
      "formattedTotalAmountIncludingTaxes": undefined,
      "formattedTotalDiscounts": undefined,
      "formattedTotalFees": undefined,
      "formattedTotalLocalDiscounts": undefined,
      "formattedTotalPrice": undefined,
      "formattedUnitFee": undefined,
      "formattedUnitPrice": undefined,
      "id": "v4-uuid-20",
      "identifier": "v4-uuid-11",
      "name": "Product 16",
      "packaging": "1",
      "quantity": "25",
      "stock": "120",
      "stockKeepingUnit": "1479427200011",
      "taxes": Array [
        Object {
          "amount": undefined,
          "formattedAmount": undefined,
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": undefined,
      "totalAmountExcludingTaxes": undefined,
      "totalAmountIncludingTaxes": undefined,
      "totalDiscounts": undefined,
      "totalFees": undefined,
      "totalGlobalDiscounts": undefined,
      "totalLocalDiscounts": undefined,
      "totalPrice": undefined,
      "totalTaxes": undefined,
      "totalTaxesExcludingGlobalDiscount": undefined,
      "unitCost": undefined,
      "unitFee": undefined,
      "unitPrice": 5.69,
    },
    3,
  ],
}
`;
