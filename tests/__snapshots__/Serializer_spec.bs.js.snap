// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Serializer cart normalization should match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 5,
  "discounts": Array [],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10000 €",
      "kind": 0,
    },
    Object {
      "amount": "0.9",
      "formattedAmount": "0.90000 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60000 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "467.68000 €",
  "formattedTotalAmountExcludingTaxes": "467.68000 €",
  "formattedTotalAmountIncludingTaxes": "561.21600 €",
  "formattedTotalAmountOfGoods": "80.08000 €",
  "formattedTotalTaxes": "93.53600 €",
  "products": Array [
    Object {
      "TAG": 1,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02000 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-47",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": "10",
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66000 €",
            "formattedTotalAmount": "66.60000 €",
            "id": "v4-uuid-44",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09000 €",
            "formattedTotalAmount": "0.90000 €",
            "id": "v4-uuid-45",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01000 €",
            "formattedTotalAmount": "320.10000 €",
            "id": "v4-uuid-46",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10.000",
        "formattedQuantity": "10.000",
        "formattedStock": "3.500",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68000 €",
        "formattedTotalAmountExcludingTaxes": "467.68000 €",
        "formattedTotalAmountIncludingTaxes": "561.21600 €",
        "formattedTotalDiscounts": "20.02000 €",
        "formattedTotalFees": "387.60000 €",
        "formattedTotalLocalDiscounts": "20.02000 €",
        "formattedTotalPrice": "100.10000 €",
        "formattedUnitFee": "38.76000 €",
        "formattedUnitPrice": "10.01000 €",
        "id": "v4-uuid-48",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": "2",
        "quantity": "10",
        "stock": "3.5",
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "93.536",
            "formattedAmount": "93.53600 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "467.68",
        "totalAmountIncludingTaxes": "561.216",
        "totalDiscounts": "20.02",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "93.536",
        "totalTaxesExcludingGlobalDiscount": "93.536",
        "unitCost": "46.768",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
      "_1": 3,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "93.536",
      "formattedAmount": "93.53600 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "467.68",
  "totalAmountExcludingTaxes": "467.68",
  "totalAmountIncludingTaxes": "561.216",
  "totalAmountOfGoods": "80.08",
  "totalDiscounts": "20.02",
  "totalProductsExpectedQuantity": 1,
  "totalProductsQuantity": 1,
  "totalTaxes": "93.536",
}
`;

exports[`Serializer cart normalization with a serialized cart which doesn't have bulk field bulk field should be added and have a falsy value after deserialization 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 5,
  "discounts": Array [],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10000 €",
      "kind": 0,
    },
    Object {
      "amount": "0.9",
      "formattedAmount": "0.90000 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60000 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "467.68000 €",
  "formattedTotalAmountExcludingTaxes": "467.68000 €",
  "formattedTotalAmountIncludingTaxes": "561.21600 €",
  "formattedTotalAmountOfGoods": "80.08000 €",
  "formattedTotalTaxes": "93.53600 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02000 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-47",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66000 €",
            "formattedTotalAmount": "66.60000 €",
            "id": "v4-uuid-44",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09000 €",
            "formattedTotalAmount": "0.90000 €",
            "id": "v4-uuid-45",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01000 €",
            "formattedTotalAmount": "320.10000 €",
            "id": "v4-uuid-46",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68000 €",
        "formattedTotalAmountExcludingTaxes": "467.68000 €",
        "formattedTotalAmountIncludingTaxes": "561.21600 €",
        "formattedTotalDiscounts": "20.02000 €",
        "formattedTotalFees": "387.60000 €",
        "formattedTotalLocalDiscounts": "20.02000 €",
        "formattedTotalPrice": "100.10000 €",
        "formattedUnitFee": "38.76000 €",
        "formattedUnitPrice": "10.01000 €",
        "id": "v4-uuid-48",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "93.536",
            "formattedAmount": "93.53600 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "467.68",
        "totalAmountIncludingTaxes": "561.216",
        "totalDiscounts": "20.02",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "93.536",
        "totalTaxesExcludingGlobalDiscount": "93.536",
        "unitCost": "46.768",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "93.536",
      "formattedAmount": "93.53600 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "467.68",
  "totalAmountExcludingTaxes": "467.68",
  "totalAmountIncludingTaxes": "561.216",
  "totalAmountOfGoods": "80.08",
  "totalDiscounts": "20.02",
  "totalProductsExpectedQuantity": 10,
  "totalProductsQuantity": 10,
  "totalTaxes": "93.536",
}
`;

exports[`Serializer cart normalization with a serialized cart which have wrong bulk value cart should have falsy bulk after deserialization 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 5,
  "discounts": Array [],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10000 €",
      "kind": 0,
    },
    Object {
      "amount": "0.9",
      "formattedAmount": "0.90000 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60000 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "467.68000 €",
  "formattedTotalAmountExcludingTaxes": "467.68000 €",
  "formattedTotalAmountIncludingTaxes": "561.21600 €",
  "formattedTotalAmountOfGoods": "80.08000 €",
  "formattedTotalTaxes": "93.53600 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02000 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-47",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66000 €",
            "formattedTotalAmount": "66.60000 €",
            "id": "v4-uuid-44",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09000 €",
            "formattedTotalAmount": "0.90000 €",
            "id": "v4-uuid-45",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01000 €",
            "formattedTotalAmount": "320.10000 €",
            "id": "v4-uuid-46",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68000 €",
        "formattedTotalAmountExcludingTaxes": "467.68000 €",
        "formattedTotalAmountIncludingTaxes": "561.21600 €",
        "formattedTotalDiscounts": "20.02000 €",
        "formattedTotalFees": "387.60000 €",
        "formattedTotalLocalDiscounts": "20.02000 €",
        "formattedTotalPrice": "100.10000 €",
        "formattedUnitFee": "38.76000 €",
        "formattedUnitPrice": "10.01000 €",
        "id": "v4-uuid-48",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "93.536",
            "formattedAmount": "93.53600 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "467.68",
        "totalAmountIncludingTaxes": "561.216",
        "totalDiscounts": "20.02",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "93.536",
        "totalTaxesExcludingGlobalDiscount": "93.536",
        "unitCost": "46.768",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "93.536",
      "formattedAmount": "93.53600 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "467.68",
  "totalAmountExcludingTaxes": "467.68",
  "totalAmountIncludingTaxes": "561.216",
  "totalAmountOfGoods": "80.08",
  "totalDiscounts": "20.02",
  "totalProductsExpectedQuantity": 10,
  "totalProductsQuantity": 10,
  "totalTaxes": "93.536",
}
`;

exports[`Serializer with decimalPrecision set to 2 Cart should correctly deserialise input 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [],
  "fees": Array [
    Object {
      "amount": "323.37",
      "formattedAmount": "323.37 €",
      "kind": 0,
    },
    Object {
      "amount": "11.16",
      "formattedAmount": "11.16 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "485.77 €",
  "formattedTotalAmountExcludingTaxes": "485.77 €",
  "formattedTotalAmountIncludingTaxes": "581.03 €",
  "formattedTotalAmountOfGoods": "84.64 €",
  "formattedTotalTaxes": "95.26 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "467.68 €",
        "formattedTotalAmountIncludingTaxes": "561.22 €",
        "formattedTotalDiscounts": "20.02 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "93.54",
            "formattedAmount": "93.54 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "467.68",
        "totalAmountIncludingTaxes": "561.22",
        "totalDiscounts": "20.02",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "93.54",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.77",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 1",
        "discounts": Array [
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "formattedValue": undefined,
            "id": "v4-uuid-26",
            "kind": 2,
            "name": "Remise de 0 gratuit",
            "quantity": "0",
            "value": 0,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.27 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "0.27",
          },
          Object {
            "amount": 1.09,
            "formattedAmount": "1.09 €",
            "formattedTotalAmount": "3.27 €",
            "id": "v4-uuid-25",
            "kind": 0,
            "totalAmount": "3.27",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "10",
        "formattedTotalAmountExcludingGlobalDiscounts": "7.53 €",
        "formattedTotalAmountExcludingTaxes": "7.53 €",
        "formattedTotalAmountIncludingTaxes": "9.03 €",
        "formattedTotalDiscounts": "0.00 €",
        "formattedTotalFees": "3.54 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "3.99 €",
        "formattedUnitFee": "1.18 €",
        "formattedUnitPrice": "1.33 €",
        "id": "v4-uuid-27",
        "identifier": "v4-uuid-1",
        "name": "Product 1",
        "packaging": 1,
        "quantity": 3,
        "stock": 10,
        "stockKeepingUnit": "1479427200001",
        "taxes": Array [
          Object {
            "amount": "1.5",
            "formattedAmount": "1.50 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "7.53",
        "totalAmountExcludingTaxes": "7.53",
        "totalAmountIncludingTaxes": "9.03",
        "totalDiscounts": "0",
        "totalFees": "3.54",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "3.99",
        "totalTaxes": "1.5",
        "totalTaxesExcludingGlobalDiscount": "1.5",
        "unitCost": "2.51",
        "unitFee": "1.18",
        "unitPrice": 1.33,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-28",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.56 €",
        "formattedTotalAmountIncludingTaxes": "10.78 €",
        "formattedTotalDiscounts": "0.00 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-29",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.56",
        "totalAmountIncludingTaxes": "10.78",
        "totalDiscounts": "0",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.52",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 8",
        "discounts": Array [
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "formattedValue": "6.66 €",
            "id": "v4-uuid-30",
            "kind": 0,
            "name": "Remise 6.66 currency",
            "quantity": "0",
            "value": 6.66,
            "warnings": Array [
              0,
            ],
          },
        ],
        "expectedQuantity": 0,
        "expectedQuantityWarning": Array [],
        "fees": Array [],
        "formattedExpectedQuantity": "0",
        "formattedQuantity": "0",
        "formattedStock": "-1",
        "formattedTotalAmountExcludingGlobalDiscounts": "0.00 €",
        "formattedTotalAmountExcludingTaxes": "0.00 €",
        "formattedTotalAmountIncludingTaxes": "0.00 €",
        "formattedTotalDiscounts": "0.00 €",
        "formattedTotalFees": "0.00 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.00 €",
        "formattedUnitFee": "0.00 €",
        "formattedUnitPrice": "20.13 €",
        "id": "v4-uuid-31",
        "identifier": "v4-uuid-5",
        "name": "Product 8",
        "packaging": 1,
        "quantity": 0,
        "stock": -1,
        "stockKeepingUnit": "1479427200005",
        "taxes": Array [
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "0",
        "totalAmountExcludingTaxes": "0",
        "totalAmountIncludingTaxes": "0",
        "totalDiscounts": "0",
        "totalFees": "0",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "0",
        "totalTaxes": "0",
        "totalTaxesExcludingGlobalDiscount": "0",
        "unitCost": undefined,
        "unitFee": "0",
        "unitPrice": 20.1281,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "95.04",
      "formattedAmount": "95.04 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "485.77",
  "totalAmountExcludingTaxes": "485.77",
  "totalAmountIncludingTaxes": "581.03",
  "totalAmountOfGoods": "84.64",
  "totalDiscounts": "20.02",
  "totalProductsExpectedQuantity": 16,
  "totalProductsQuantity": 16,
  "totalTaxes": "95.26",
}
`;

exports[`Serializer with decimalPrecision set to 2 Cart should correctly serialise input 1`] = `"{\\"products\\":[{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-23\\",\\"identifier\\":\\"v4-uuid-3\\",\\"stockKeepingUnit\\":\\"1479427200003\\",\\"name\\":\\"Product 3\\",\\"description\\":\\"Description of product 3\\",\\"stock\\":3,\\"packaging\\":7,\\"quantity\\":10,\\"expectedQuantity\\":10,\\"unitPrice\\":\\"10.01\\",\\"fees\\":[{\\"id\\":\\"v4-uuid-19\\",\\"kind\\":\\"other\\",\\"amount\\":\\"6.66\\"},{\\"id\\":\\"v4-uuid-20\\",\\"kind\\":\\"taxes\\",\\"amount\\":\\"0.09\\"},{\\"id\\":\\"v4-uuid-21\\",\\"kind\\":\\"transport\\",\\"amount\\":\\"32.01\\"}],\\"discounts\\":[{\\"id\\":\\"v4-uuid-22\\",\\"name\\":\\"Remise 20%\\",\\"kind\\":\\"percent\\",\\"value\\":\\"20\\",\\"quantity\\":0}],\\"taxes\\":[{\\"rate\\":\\"20\\"}]}},{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-27\\",\\"identifier\\":\\"v4-uuid-1\\",\\"stockKeepingUnit\\":\\"1479427200001\\",\\"name\\":\\"Product 1\\",\\"description\\":\\"Description of product 1\\",\\"stock\\":10,\\"packaging\\":1,\\"quantity\\":3,\\"expectedQuantity\\":3,\\"unitPrice\\":\\"1.33\\",\\"fees\\":[{\\"id\\":\\"v4-uuid-24\\",\\"kind\\":\\"taxes\\",\\"amount\\":\\"0.09\\"},{\\"id\\":\\"v4-uuid-25\\",\\"kind\\":\\"transport\\",\\"amount\\":\\"1.09\\"}],\\"discounts\\":[{\\"id\\":\\"v4-uuid-26\\",\\"name\\":\\"Remise de 0 gratuit\\",\\"kind\\":\\"free\\",\\"value\\":\\"0\\",\\"quantity\\":0}],\\"taxes\\":[{\\"rate\\":\\"20\\"}]}},{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-29\\",\\"identifier\\":\\"v4-uuid-7\\",\\"stockKeepingUnit\\":\\"1479427200007\\",\\"name\\":\\"Product 10\\",\\"description\\":\\"Description of product 10\\",\\"stock\\":33,\\"packaging\\":3,\\"quantity\\":3,\\"expectedQuantity\\":3,\\"unitPrice\\":\\"0.19\\",\\"fees\\":[{\\"id\\":\\"v4-uuid-28\\",\\"kind\\":\\"taxes\\",\\"amount\\":\\"3.33\\"}],\\"discounts\\":[],\\"taxes\\":[{\\"rate\\":\\"2.1\\"},{\\"rate\\":\\"20\\"}]}},{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-31\\",\\"identifier\\":\\"v4-uuid-5\\",\\"stockKeepingUnit\\":\\"1479427200005\\",\\"name\\":\\"Product 8\\",\\"description\\":\\"Description of product 8\\",\\"stock\\":-1,\\"packaging\\":1,\\"quantity\\":0,\\"expectedQuantity\\":0,\\"unitPrice\\":\\"20.1281\\",\\"fees\\":[],\\"discounts\\":[{\\"id\\":\\"v4-uuid-30\\",\\"name\\":\\"Remise 6.66 currency\\",\\"kind\\":\\"currency\\",\\"value\\":\\"6.66\\",\\"quantity\\":0}],\\"taxes\\":[{\\"rate\\":\\"20\\"}]}}],\\"discounts\\":[],\\"decimalPrecision\\":2,\\"currency\\":\\"eur\\",\\"taxesFree\\":false,\\"standardTaxRate\\":20}"`;

exports[`Serializer with decimalPrecision set to 3 Cart should correctly deserialise input 1`] = `
Object {
  "currency": 1,
  "decimalPrecision": 3,
  "discounts": Array [],
  "fees": Array [
    Object {
      "amount": "9638.28",
      "formattedAmount": "$9638.280",
      "kind": 0,
    },
    Object {
      "amount": "10.26",
      "formattedAmount": "$10.260",
      "kind": 1,
    },
    Object {
      "amount": "2004.66",
      "formattedAmount": "$2004.660",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "$17648.637",
  "formattedTotalAmountExcludingTaxes": "$17648.637",
  "formattedTotalAmountIncludingTaxes": "$21176.474",
  "formattedTotalAmountOfGoods": "$5995.437",
  "formattedTotalTaxes": "$3527.837",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          1,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 18",
        "discounts": Array [
          Object {
            "amount": "6.66",
            "formattedAmount": "$6.660",
            "formattedValue": "$6.660",
            "id": "v4-uuid-34",
            "kind": 0,
            "name": "Remise 6.66 currency",
            "quantity": "0",
            "value": 6.66,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 301,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "$6.660",
            "formattedTotalAmount": "$2004.660",
            "id": "v4-uuid-32",
            "kind": 2,
            "totalAmount": "2004.66",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "$32.010",
            "formattedTotalAmount": "$9635.010",
            "id": "v4-uuid-33",
            "kind": 0,
            "totalAmount": "9635.01",
          },
        ],
        "formattedExpectedQuantity": "301",
        "formattedQuantity": "301",
        "formattedStock": "0",
        "formattedTotalAmountExcludingGlobalDiscounts": "$17617.191",
        "formattedTotalAmountExcludingTaxes": "$17617.191",
        "formattedTotalAmountIncludingTaxes": "$21140.629",
        "formattedTotalDiscounts": "$6.660",
        "formattedTotalFees": "$11639.670",
        "formattedTotalLocalDiscounts": "$6.660",
        "formattedTotalPrice": "$5984.181",
        "formattedUnitFee": "$38.670",
        "formattedUnitPrice": "$19.881",
        "id": "v4-uuid-35",
        "identifier": "v4-uuid-8",
        "name": "Product 18",
        "packaging": 3,
        "quantity": 301,
        "stock": 0,
        "stockKeepingUnit": "1479427200008",
        "taxes": Array [
          Object {
            "amount": "3523.438",
            "formattedAmount": "$3523.438",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "17617.191",
        "totalAmountExcludingTaxes": "17617.191",
        "totalAmountIncludingTaxes": "21140.629",
        "totalDiscounts": "6.66",
        "totalFees": "11639.67",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "6.66",
        "totalPrice": "5984.181",
        "totalTaxes": "3523.438",
        "totalTaxesExcludingGlobalDiscount": "3523.438",
        "unitCost": "58.529",
        "unitFee": "38.67",
        "unitPrice": 19.881,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 1",
        "discounts": Array [
          Object {
            "amount": "0",
            "formattedAmount": "$0.000",
            "formattedValue": undefined,
            "id": "v4-uuid-38",
            "kind": 2,
            "name": "Remise de 0 gratuit",
            "quantity": "0",
            "value": 0,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 0.09,
            "formattedAmount": "$0.090",
            "formattedTotalAmount": "$0.270",
            "id": "v4-uuid-36",
            "kind": 1,
            "totalAmount": "0.27",
          },
          Object {
            "amount": 1.09,
            "formattedAmount": "$1.090",
            "formattedTotalAmount": "$3.270",
            "id": "v4-uuid-37",
            "kind": 0,
            "totalAmount": "3.27",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "10",
        "formattedTotalAmountExcludingGlobalDiscounts": "$7.530",
        "formattedTotalAmountExcludingTaxes": "$7.530",
        "formattedTotalAmountIncludingTaxes": "$9.036",
        "formattedTotalDiscounts": "$0.000",
        "formattedTotalFees": "$3.540",
        "formattedTotalLocalDiscounts": "$0.000",
        "formattedTotalPrice": "$3.990",
        "formattedUnitFee": "$1.180",
        "formattedUnitPrice": "$1.330",
        "id": "v4-uuid-39",
        "identifier": "v4-uuid-1",
        "name": "Product 1",
        "packaging": 1,
        "quantity": 3,
        "stock": 10,
        "stockKeepingUnit": "1479427200001",
        "taxes": Array [
          Object {
            "amount": "1.506",
            "formattedAmount": "$1.506",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "7.53",
        "totalAmountExcludingTaxes": "7.53",
        "totalAmountIncludingTaxes": "9.036",
        "totalDiscounts": "0",
        "totalFees": "3.54",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "3.99",
        "totalTaxes": "1.506",
        "totalTaxesExcludingGlobalDiscount": "1.506",
        "unitCost": "2.51",
        "unitFee": "1.18",
        "unitPrice": 1.33,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "$3.330",
            "formattedTotalAmount": "$9.990",
            "id": "v4-uuid-40",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "$10.560",
        "formattedTotalAmountExcludingTaxes": "$10.560",
        "formattedTotalAmountIncludingTaxes": "$10.782",
        "formattedTotalDiscounts": "$0.000",
        "formattedTotalFees": "$9.990",
        "formattedTotalLocalDiscounts": "$0.000",
        "formattedTotalPrice": "$0.570",
        "formattedUnitFee": "$3.330",
        "formattedUnitPrice": "$0.190",
        "id": "v4-uuid-41",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.222",
            "formattedAmount": "$0.222",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "$0.000",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.56",
        "totalAmountIncludingTaxes": "10.782",
        "totalDiscounts": "0",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.222",
        "totalTaxesExcludingGlobalDiscount": "0.222",
        "unitCost": "3.52",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
    Object {
      "TAG": 1,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
          2,
        ],
        "capacityUnit": "kg",
        "description": "Description of product 17",
        "discounts": Array [
          Object {
            "amount": "3.339",
            "formattedAmount": "$3.339",
            "formattedValue": "20 %",
            "id": "v4-uuid-42",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [
              0,
            ],
          },
        ],
        "expectedQuantity": "5",
        "expectedQuantityWarning": Array [],
        "fees": Array [],
        "formattedExpectedQuantity": "5.00 kg",
        "formattedQuantity": "5.00 kg",
        "formattedStock": "12.00 kg",
        "formattedTotalAmountExcludingGlobalDiscounts": "$13.356",
        "formattedTotalAmountExcludingTaxes": "$13.356",
        "formattedTotalAmountIncludingTaxes": "$16.027",
        "formattedTotalDiscounts": "$3.339",
        "formattedTotalFees": "$0.000",
        "formattedTotalLocalDiscounts": "$3.339",
        "formattedTotalPrice": "$16.695/kg",
        "formattedUnitFee": "$0.000/kg",
        "formattedUnitPrice": "$3.339/kg",
        "id": "v4-uuid-43",
        "identifier": "v4-uuid-12",
        "name": "Product 17",
        "packaging": "1",
        "quantity": "5",
        "stock": "12",
        "stockKeepingUnit": "1479427200012",
        "taxes": Array [
          Object {
            "amount": "2.671",
            "formattedAmount": "$2.671",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "13.356",
        "totalAmountExcludingTaxes": "13.356",
        "totalAmountIncludingTaxes": "16.027",
        "totalDiscounts": "3.339",
        "totalFees": "0",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "3.339",
        "totalPrice": "16.695",
        "totalTaxes": "2.671",
        "totalTaxesExcludingGlobalDiscount": "2.671",
        "unitCost": "2.671",
        "unitFee": "0",
        "unitPrice": 3.339,
      },
      "_1": 2,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.222",
      "formattedAmount": "$0.222",
      "rate": 2.1,
    },
    Object {
      "amount": "3527.615",
      "formattedAmount": "$3527.615",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "17648.637",
  "totalAmountExcludingTaxes": "17648.637",
  "totalAmountIncludingTaxes": "21176.474",
  "totalAmountOfGoods": "5995.437",
  "totalDiscounts": "9.999",
  "totalProductsExpectedQuantity": 308,
  "totalProductsQuantity": 308,
  "totalTaxes": "3527.837",
}
`;

exports[`Serializer with decimalPrecision set to 3 Cart should correctly serialise input 1`] = `"{\\"products\\":[{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-35\\",\\"identifier\\":\\"v4-uuid-8\\",\\"stockKeepingUnit\\":\\"1479427200008\\",\\"name\\":\\"Product 18\\",\\"description\\":\\"Description of product 18\\",\\"stock\\":0,\\"packaging\\":3,\\"quantity\\":301,\\"expectedQuantity\\":301,\\"unitPrice\\":\\"19.881\\",\\"fees\\":[{\\"id\\":\\"v4-uuid-32\\",\\"kind\\":\\"other\\",\\"amount\\":\\"6.66\\"},{\\"id\\":\\"v4-uuid-33\\",\\"kind\\":\\"transport\\",\\"amount\\":\\"32.01\\"}],\\"discounts\\":[{\\"id\\":\\"v4-uuid-34\\",\\"name\\":\\"Remise 6.66 currency\\",\\"kind\\":\\"currency\\",\\"value\\":\\"6.66\\",\\"quantity\\":0}],\\"taxes\\":[{\\"rate\\":\\"20\\"}]}},{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-39\\",\\"identifier\\":\\"v4-uuid-1\\",\\"stockKeepingUnit\\":\\"1479427200001\\",\\"name\\":\\"Product 1\\",\\"description\\":\\"Description of product 1\\",\\"stock\\":10,\\"packaging\\":1,\\"quantity\\":3,\\"expectedQuantity\\":3,\\"unitPrice\\":\\"1.33\\",\\"fees\\":[{\\"id\\":\\"v4-uuid-36\\",\\"kind\\":\\"taxes\\",\\"amount\\":\\"0.09\\"},{\\"id\\":\\"v4-uuid-37\\",\\"kind\\":\\"transport\\",\\"amount\\":\\"1.09\\"}],\\"discounts\\":[{\\"id\\":\\"v4-uuid-38\\",\\"name\\":\\"Remise de 0 gratuit\\",\\"kind\\":\\"free\\",\\"value\\":\\"0\\",\\"quantity\\":0}],\\"taxes\\":[{\\"rate\\":\\"20\\"}]}},{\\"TAG\\":0,\\"product\\":{\\"id\\":\\"v4-uuid-41\\",\\"identifier\\":\\"v4-uuid-7\\",\\"stockKeepingUnit\\":\\"1479427200007\\",\\"name\\":\\"Product 10\\",\\"description\\":\\"Description of product 10\\",\\"stock\\":33,\\"packaging\\":3,\\"quantity\\":3,\\"expectedQuantity\\":3,\\"unitPrice\\":\\"0.19\\",\\"fees\\":[{\\"id\\":\\"v4-uuid-40\\",\\"kind\\":\\"taxes\\",\\"amount\\":\\"3.33\\"}],\\"discounts\\":[],\\"taxes\\":[{\\"rate\\":\\"2.1\\"},{\\"rate\\":\\"20\\"}]}},{\\"TAG\\":1,\\"product\\":{\\"id\\":\\"v4-uuid-43\\",\\"identifier\\":\\"v4-uuid-12\\",\\"stockKeepingUnit\\":\\"1479427200012\\",\\"name\\":\\"Product 17\\",\\"description\\":\\"Description of product 17\\",\\"capacityUnit\\":\\"kg\\",\\"stock\\":12,\\"packaging\\":1,\\"quantity\\":5,\\"expectedQuantity\\":5,\\"unitPrice\\":\\"3.339\\",\\"fees\\":[],\\"discounts\\":[{\\"id\\":\\"v4-uuid-42\\",\\"name\\":\\"Remise 20%\\",\\"kind\\":\\"percent\\",\\"value\\":\\"20\\",\\"quantity\\":0}],\\"taxes\\":[{\\"rate\\":\\"20\\"}]},\\"precision\\":2}],\\"discounts\\":[],\\"decimalPrecision\\":3,\\"currency\\":\\"usd\\",\\"taxesFree\\":false,\\"standardTaxRate\\":20}"`;
