// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Make makeCart with errors should raise an error if cart input has many discounts 1`] = `"Cart must not have more than one discount"`;

exports[`Make makeCart without errors should correctly make cart shape and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "0",
      "formattedAmount": undefined,
      "formattedValue": undefined,
      "id": "v4-uuid-40",
      "kind": 1,
      "name": "Remise 20%",
      "quantity": "0",
      "value": 20,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "96.03",
      "formattedAmount": undefined,
      "kind": 0,
    },
    Object {
      "amount": "9.99",
      "formattedAmount": undefined,
      "kind": 1,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": undefined,
  "formattedTotalAmountExcludingTaxes": undefined,
  "formattedTotalAmountIncludingTaxes": undefined,
  "formattedTotalAmountOfGoods": undefined,
  "formattedTotalTaxes": undefined,
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [
          Object {
            "amount": "0.57",
            "formattedAmount": undefined,
            "formattedValue": undefined,
            "id": "v4-uuid-38",
            "kind": 0,
            "name": "Remise 6.66 currency",
            "quantity": "0",
            "value": 6.66,
            "warnings": Array [
              0,
            ],
          },
        ],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-36",
            "kind": 1,
            "totalAmount": "9.99",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-37",
            "kind": 0,
            "totalAmount": "96.03",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-39",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.21",
            "formattedAmount": undefined,
            "rate": 2.1,
          },
          Object {
            "amount": "19.21",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "106.02",
        "totalAmountExcludingTaxes": "106.02",
        "totalAmountIncludingTaxes": "125.44",
        "totalDiscounts": "0.57",
        "totalFees": "106.02",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0.57",
        "totalPrice": "0.57",
        "totalTaxes": "19.42",
        "totalTaxesExcludingGlobalDiscount": "19.42",
        "unitCost": "35.34",
        "unitFee": "35.34",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.21",
      "formattedAmount": undefined,
      "rate": 2.1,
    },
    Object {
      "amount": "19.21",
      "formattedAmount": undefined,
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "106.02",
  "totalAmountExcludingTaxes": "106.02",
  "totalAmountIncludingTaxes": "125.44",
  "totalAmountOfGoods": "0",
  "totalDiscounts": "0.57",
  "totalProductsExpectedQuantity": 3,
  "totalProductsQuantity": 3,
  "totalTaxes": "19.42",
}
`;

exports[`Make makeCart without errors should match snapshot of cart input 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "id": undefined,
      "kind": 1,
      "name": "Remise 20%",
      "quantity": 0,
      "value": 20,
    },
  ],
  "products": Array [
    Object {
      "TAG": 0,
      "product": Object {
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [
          Object {
            "id": undefined,
            "kind": 0,
            "name": "Remise 6.66 currency",
            "quantity": 0,
            "value": 6.66,
          },
        ],
        "expectedQuantity": undefined,
        "fees": Array [
          Object {
            "amount": 3.33,
            "id": undefined,
            "kind": 1,
          },
          Object {
            "amount": 32.01,
            "id": undefined,
            "kind": 0,
          },
        ],
        "id": undefined,
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "tax": 2.1,
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxesFree": false,
}
`;

exports[`Make makeDiscount with errors should raise an error when trying to make an incorrect discount (Currency.value) input 1`] = `"Discount currency value must be >= 0"`;

exports[`Make makeDiscount with errors should raise an error when trying to make an incorrect discount (Free.quantity) input 1`] = `"Discount quantity must be >= 0"`;

exports[`Make makeDiscount with errors should raise an error when trying to make an incorrect discount (Percent.value) input 1`] = `"Discount percent value must be >= 0 and <= 100"`;

exports[`Make makeDiscount without errors should match snapshot 1`] = `
Object {
  "amount": undefined,
  "formattedAmount": undefined,
  "formattedValue": undefined,
  "id": "v4-uuid-20",
  "kind": 1,
  "name": "Remise 3.33%",
  "quantity": "0",
  "value": 3.33,
  "warnings": Array [],
}
`;

exports[`Make makeDiscount without errors should match snapshot discount.id 1`] = `"v4-uuid-20"`;

exports[`Make makeFee with errors should raise an error when trying to make an incorrect feeInput 1`] = `"Fee amount must be >= 0"`;

exports[`Make makeFee without errors should match snapshot 1`] = `
Object {
  "amount": 0.01,
  "formattedAmount": undefined,
  "formattedTotalAmount": undefined,
  "id": "v4-uuid-19",
  "kind": 2,
  "totalAmount": undefined,
}
`;

exports[`Make makeFee without errors should match snapshot fee.id 1`] = `"v4-uuid-19"`;

exports[`Make makeProduct with errors should raise an error if product input has more than one discount 1`] = `"Product must not have more than one discounts"`;

exports[`Make makeProduct with errors should raise an error when trying to make an incorrect product (Negative quantity) input 1`] = `"Product expected quantity must be >= 0"`;

exports[`Make makeProduct with errors should raise an error when trying to make an incorrect product (Negative tax) input 1`] = `"Product tax must be >= 0"`;

exports[`Make makeProduct with errors should raise an error when trying to make an incorrect product (Negative unitPrice) input 1`] = `"Product unitPrice must >= 0"`;

exports[`Make makeProduct without errors should correctly make shape from a product input - product with discounts 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": undefined,
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": undefined,
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-32",
        "kind": 0,
        "name": "Remise 12.09 currency",
        "quantity": "0",
        "value": 12.09,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [],
    "fees": Array [],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-33",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": undefined,
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": undefined,
    "totalAmountExcludingTaxes": undefined,
    "totalAmountIncludingTaxes": undefined,
    "totalDiscounts": undefined,
    "totalFees": undefined,
    "totalGlobalDiscounts": undefined,
    "totalLocalDiscounts": undefined,
    "totalPrice": undefined,
    "totalTaxes": undefined,
    "totalTaxesExcludingGlobalDiscount": undefined,
    "unitCost": undefined,
    "unitFee": undefined,
    "unitPrice": 10.01,
  },
}
`;

exports[`Make makeProduct without errors should correctly make shape from a product input - product with fees 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": undefined,
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [],
    "fees": Array [
      Object {
        "amount": 3.33,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-34",
        "kind": 1,
        "totalAmount": undefined,
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": undefined,
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": undefined,
    "totalAmountExcludingTaxes": undefined,
    "totalAmountIncludingTaxes": undefined,
    "totalDiscounts": undefined,
    "totalFees": undefined,
    "totalGlobalDiscounts": undefined,
    "totalLocalDiscounts": undefined,
    "totalPrice": undefined,
    "totalTaxes": undefined,
    "totalTaxesExcludingGlobalDiscount": undefined,
    "unitCost": undefined,
    "unitFee": undefined,
    "unitPrice": 10.01,
  },
}
`;

exports[`Make makeProduct without errors should correctly make shape from a product input 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": undefined,
    "capacityUnit": undefined,
    "description": "Description of product 9",
    "discounts": Array [],
    "expectedQuantity": 13,
    "expectedQuantityWarning": Array [],
    "fees": Array [],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-31",
    "identifier": "v4-uuid-6",
    "name": "Product 9",
    "packaging": 3,
    "quantity": 13,
    "stock": 3,
    "stockKeepingUnit": "1479427200006",
    "taxes": Array [
      Object {
        "amount": undefined,
        "formattedAmount": undefined,
        "rate": 10,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": undefined,
    "totalAmountExcludingTaxes": undefined,
    "totalAmountIncludingTaxes": undefined,
    "totalDiscounts": undefined,
    "totalFees": undefined,
    "totalGlobalDiscounts": undefined,
    "totalLocalDiscounts": undefined,
    "totalPrice": undefined,
    "totalTaxes": undefined,
    "totalTaxesExcludingGlobalDiscount": undefined,
    "unitCost": undefined,
    "unitFee": undefined,
    "unitPrice": 20.11,
  },
}
`;
