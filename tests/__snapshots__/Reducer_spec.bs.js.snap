// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`process with bulk discounted product and global discount should process cart and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "0.09",
      "formattedAmount": "0.09 €",
      "formattedValue": "10 %",
      "id": "v4-uuid-67",
      "kind": 1,
      "name": "Remise 10%",
      "quantity": "0",
      "value": 10,
      "warnings": Array [
        0,
      ],
    },
  ],
  "fees": Array [],
  "formattedTotalAmountExcludingGlobalDiscounts": "0.90 €",
  "formattedTotalAmountExcludingTaxes": "0.81 €",
  "formattedTotalAmountIncludingTaxes": "0.97 €",
  "formattedTotalAmountOfGoods": "0.81 €",
  "formattedTotalTaxes": "0.16 €",
  "products": Array [
    Object {
      "TAG": 1,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
          2,
        ],
        "capacityUnit": "kg",
        "description": "description",
        "discounts": Array [
          Object {
            "amount": "0.1",
            "formattedAmount": "0.10 €",
            "formattedValue": "10 %",
            "id": "v4-uuid-65",
            "kind": 1,
            "name": "Remise 10%",
            "quantity": "0",
            "value": 10,
            "warnings": Array [
              0,
            ],
          },
        ],
        "expectedQuantity": "1",
        "expectedQuantityWarning": Array [],
        "fees": Array [],
        "formattedExpectedQuantity": "1.000 kg",
        "formattedQuantity": "1.000 kg",
        "formattedStock": "5.000 kg",
        "formattedTotalAmountExcludingGlobalDiscounts": "0.90 €",
        "formattedTotalAmountExcludingTaxes": "0.81 €",
        "formattedTotalAmountIncludingTaxes": "0.97 €",
        "formattedTotalDiscounts": "0.19 €",
        "formattedTotalFees": "0.00 €",
        "formattedTotalLocalDiscounts": "0.10 €",
        "formattedTotalPrice": "1.00 €/kg",
        "formattedUnitFee": "0.00 €/kg",
        "formattedUnitPrice": "1.00 €/kg",
        "id": "v4-uuid-66",
        "identifier": undefined,
        "name": "name",
        "packaging": "1",
        "quantity": "1",
        "stock": "5",
        "stockKeepingUnit": undefined,
        "taxes": Array [
          Object {
            "amount": "0.16",
            "formattedAmount": "0.16 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "0.9",
        "totalAmountExcludingTaxes": "0.81",
        "totalAmountIncludingTaxes": "0.97",
        "totalDiscounts": "0.19",
        "totalFees": "0",
        "totalGlobalDiscounts": "0.09",
        "totalLocalDiscounts": "0.1",
        "totalPrice": "1",
        "totalTaxes": "0.16",
        "totalTaxesExcludingGlobalDiscount": "0.18",
        "unitCost": "0.81",
        "unitFee": "0",
        "unitPrice": 1,
      },
      "_1": 3,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.16",
      "formattedAmount": "0.16 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "0.9",
  "totalAmountExcludingTaxes": "0.81",
  "totalAmountIncludingTaxes": "0.97",
  "totalAmountOfGoods": "0.81",
  "totalDiscounts": "0.19",
  "totalProductsExpectedQuantity": 1,
  "totalProductsQuantity": 1,
  "totalTaxes": "0.16",
}
`;

exports[`process with bulk discounted product should process cart and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [],
  "fees": Array [],
  "formattedTotalAmountExcludingGlobalDiscounts": "0.90 €",
  "formattedTotalAmountExcludingTaxes": "0.90 €",
  "formattedTotalAmountIncludingTaxes": "1.08 €",
  "formattedTotalAmountOfGoods": "0.90 €",
  "formattedTotalTaxes": "0.18 €",
  "products": Array [
    Object {
      "TAG": 1,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
          2,
        ],
        "capacityUnit": "kg",
        "description": "description",
        "discounts": Array [
          Object {
            "amount": "0.1",
            "formattedAmount": "0.10 €",
            "formattedValue": "10 %",
            "id": "v4-uuid-61",
            "kind": 1,
            "name": "Remise 10%",
            "quantity": "0",
            "value": 10,
            "warnings": Array [
              0,
            ],
          },
        ],
        "expectedQuantity": "1",
        "expectedQuantityWarning": Array [],
        "fees": Array [],
        "formattedExpectedQuantity": "1.000 kg",
        "formattedQuantity": "1.000 kg",
        "formattedStock": "5.000 kg",
        "formattedTotalAmountExcludingGlobalDiscounts": "0.90 €",
        "formattedTotalAmountExcludingTaxes": "0.90 €",
        "formattedTotalAmountIncludingTaxes": "1.08 €",
        "formattedTotalDiscounts": "0.10 €",
        "formattedTotalFees": "0.00 €",
        "formattedTotalLocalDiscounts": "0.10 €",
        "formattedTotalPrice": "1.00 €/kg",
        "formattedUnitFee": "0.00 €/kg",
        "formattedUnitPrice": "1.00 €/kg",
        "id": "v4-uuid-62",
        "identifier": undefined,
        "name": "name",
        "packaging": "1",
        "quantity": "1",
        "stock": "5",
        "stockKeepingUnit": undefined,
        "taxes": Array [
          Object {
            "amount": "0.18",
            "formattedAmount": "0.18 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "0.9",
        "totalAmountExcludingTaxes": "0.9",
        "totalAmountIncludingTaxes": "1.08",
        "totalDiscounts": "0.1",
        "totalFees": "0",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0.1",
        "totalPrice": "1",
        "totalTaxes": "0.18",
        "totalTaxesExcludingGlobalDiscount": "0.18",
        "unitCost": "0.9",
        "unitFee": "0",
        "unitPrice": 1,
      },
      "_1": 3,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.18",
      "formattedAmount": "0.18 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "0.9",
  "totalAmountExcludingTaxes": "0.9",
  "totalAmountIncludingTaxes": "1.08",
  "totalAmountOfGoods": "0.9",
  "totalDiscounts": "0.1",
  "totalProductsExpectedQuantity": 1,
  "totalProductsQuantity": 1,
  "totalTaxes": "0.18",
}
`;

exports[`process with bulk product and global discount should process cart and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "0.1",
      "formattedAmount": "0.10 €",
      "formattedValue": "10 %",
      "id": "v4-uuid-64",
      "kind": 1,
      "name": "Remise 10%",
      "quantity": "0",
      "value": 10,
      "warnings": Array [
        0,
      ],
    },
  ],
  "fees": Array [],
  "formattedTotalAmountExcludingGlobalDiscounts": "1.00 €",
  "formattedTotalAmountExcludingTaxes": "0.90 €",
  "formattedTotalAmountIncludingTaxes": "1.08 €",
  "formattedTotalAmountOfGoods": "0.90 €",
  "formattedTotalTaxes": "0.18 €",
  "products": Array [
    Object {
      "TAG": 1,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
          2,
        ],
        "capacityUnit": "kg",
        "description": "description",
        "discounts": Array [],
        "expectedQuantity": "1",
        "expectedQuantityWarning": Array [],
        "fees": Array [],
        "formattedExpectedQuantity": "1.000 kg",
        "formattedQuantity": "1.000 kg",
        "formattedStock": "5.000 kg",
        "formattedTotalAmountExcludingGlobalDiscounts": "1.00 €",
        "formattedTotalAmountExcludingTaxes": "0.90 €",
        "formattedTotalAmountIncludingTaxes": "1.08 €",
        "formattedTotalDiscounts": "0.10 €",
        "formattedTotalFees": "0.00 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "1.00 €/kg",
        "formattedUnitFee": "0.00 €/kg",
        "formattedUnitPrice": "1.00 €/kg",
        "id": "v4-uuid-63",
        "identifier": undefined,
        "name": "name",
        "packaging": "1",
        "quantity": "1",
        "stock": "5",
        "stockKeepingUnit": undefined,
        "taxes": Array [
          Object {
            "amount": "0.18",
            "formattedAmount": "0.18 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "1",
        "totalAmountExcludingTaxes": "0.9",
        "totalAmountIncludingTaxes": "1.08",
        "totalDiscounts": "0.1",
        "totalFees": "0",
        "totalGlobalDiscounts": "0.1",
        "totalLocalDiscounts": "0",
        "totalPrice": "1",
        "totalTaxes": "0.18",
        "totalTaxesExcludingGlobalDiscount": "0.2",
        "unitCost": "0.9",
        "unitFee": "0",
        "unitPrice": 1,
      },
      "_1": 3,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.18",
      "formattedAmount": "0.18 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "1",
  "totalAmountExcludingTaxes": "0.9",
  "totalAmountIncludingTaxes": "1.08",
  "totalAmountOfGoods": "0.9",
  "totalDiscounts": "0.1",
  "totalProductsExpectedQuantity": 1,
  "totalProductsQuantity": 1,
  "totalTaxes": "0.18",
}
`;

exports[`process with bulk product should process cart and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [],
  "fees": Array [],
  "formattedTotalAmountExcludingGlobalDiscounts": "1.00 €",
  "formattedTotalAmountExcludingTaxes": "1.00 €",
  "formattedTotalAmountIncludingTaxes": "1.20 €",
  "formattedTotalAmountOfGoods": "1.00 €",
  "formattedTotalTaxes": "0.20 €",
  "products": Array [
    Object {
      "TAG": 1,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
          2,
        ],
        "capacityUnit": "kg",
        "description": "description",
        "discounts": Array [],
        "expectedQuantity": "1",
        "expectedQuantityWarning": Array [],
        "fees": Array [],
        "formattedExpectedQuantity": "1.000 kg",
        "formattedQuantity": "1.000 kg",
        "formattedStock": "5.000 kg",
        "formattedTotalAmountExcludingGlobalDiscounts": "1.00 €",
        "formattedTotalAmountExcludingTaxes": "1.00 €",
        "formattedTotalAmountIncludingTaxes": "1.20 €",
        "formattedTotalDiscounts": "0.00 €",
        "formattedTotalFees": "0.00 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "1.00 €/kg",
        "formattedUnitFee": "0.00 €/kg",
        "formattedUnitPrice": "1.00 €/kg",
        "id": "v4-uuid-60",
        "identifier": undefined,
        "name": "name",
        "packaging": "1",
        "quantity": "1",
        "stock": "5",
        "stockKeepingUnit": undefined,
        "taxes": Array [
          Object {
            "amount": "0.2",
            "formattedAmount": "0.20 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "1",
        "totalAmountExcludingTaxes": "1",
        "totalAmountIncludingTaxes": "1.2",
        "totalDiscounts": "0",
        "totalFees": "0",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "1",
        "totalTaxes": "0.2",
        "totalTaxesExcludingGlobalDiscount": "0.2",
        "unitCost": "1",
        "unitFee": "0",
        "unitPrice": 1,
      },
      "_1": 3,
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.2",
      "formattedAmount": "0.20 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "1",
  "totalAmountExcludingTaxes": "1",
  "totalAmountIncludingTaxes": "1.2",
  "totalAmountOfGoods": "1",
  "totalDiscounts": "0",
  "totalProductsExpectedQuantity": 1,
  "totalProductsQuantity": 1,
  "totalTaxes": "0.2",
}
`;

exports[`reducer BatchProductAdded should match snapshot on first bulk product added 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        1,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 8",
      "discounts": Array [
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "formattedValue": "6.66 €",
          "id": "v4-uuid-39",
          "kind": 0,
          "name": "Remise 6.66 currency",
          "quantity": "0",
          "value": 6.66,
          "warnings": Array [
            0,
          ],
        },
      ],
      "expectedQuantity": 0,
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": "0",
      "formattedQuantity": "0",
      "formattedStock": "-1",
      "formattedTotalAmountExcludingGlobalDiscounts": "0.00 €",
      "formattedTotalAmountExcludingTaxes": "0.00 €",
      "formattedTotalAmountIncludingTaxes": "0.00 €",
      "formattedTotalDiscounts": "0.00 €",
      "formattedTotalFees": "0.00 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.00 €",
      "formattedUnitFee": "0.00 €",
      "formattedUnitPrice": "20.13 €",
      "id": "v4-uuid-40",
      "identifier": "v4-uuid-5",
      "name": "Product 8",
      "packaging": 1,
      "quantity": 0,
      "stock": -1,
      "stockKeepingUnit": "1479427200005",
      "taxes": Array [
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "0",
      "totalAmountExcludingTaxes": "0",
      "totalAmountIncludingTaxes": "0",
      "totalDiscounts": "0",
      "totalFees": "0",
      "totalGlobalDiscounts": "0",
      "totalLocalDiscounts": "0",
      "totalPrice": "0",
      "totalTaxes": "0",
      "totalTaxesExcludingGlobalDiscount": "0",
      "unitCost": undefined,
      "unitFee": "0",
      "unitPrice": 20.1281,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        1,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 9",
      "discounts": Array [
        Object {
          "amount": "8.71",
          "formattedAmount": "8.71 €",
          "formattedValue": "3.33 %",
          "id": "v4-uuid-35",
          "kind": 1,
          "name": "Remise 3.33%",
          "quantity": "0",
          "value": 3.33,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 13,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "416.13 €",
          "id": "v4-uuid-34",
          "kind": 0,
          "totalAmount": "416.13",
        },
      ],
      "formattedExpectedQuantity": "13",
      "formattedQuantity": "13",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "668.85 €",
      "formattedTotalAmountExcludingTaxes": "664.97 €",
      "formattedTotalAmountIncludingTaxes": "773.08 €",
      "formattedTotalDiscounts": "12.59 €",
      "formattedTotalFees": "416.13 €",
      "formattedTotalLocalDiscounts": "8.71 €",
      "formattedTotalPrice": "261.43 €",
      "formattedUnitFee": "32.01 €",
      "formattedUnitPrice": "20.11 €",
      "id": "v4-uuid-36",
      "identifier": "v4-uuid-6",
      "name": "Product 9",
      "packaging": 3,
      "quantity": 13,
      "stock": 3,
      "stockKeepingUnit": "1479427200006",
      "taxes": Array [
        Object {
          "amount": "24.88",
          "formattedAmount": "24.88 €",
          "rate": 10,
        },
        Object {
          "amount": "83.23",
          "formattedAmount": "83.23 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "668.85",
      "totalAmountExcludingTaxes": "664.97",
      "totalAmountIncludingTaxes": "773.08",
      "totalDiscounts": "12.59",
      "totalFees": "416.13",
      "totalGlobalDiscounts": "3.88",
      "totalLocalDiscounts": "8.71",
      "totalPrice": "261.43",
      "totalTaxes": "108.11",
      "totalTaxesExcludingGlobalDiscount": "108.5",
      "unitCost": "51.15",
      "unitFee": "32.01",
      "unitPrice": 20.11,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [],
      "capacityUnit": undefined,
      "description": "Description of product 3",
      "discounts": Array [
        Object {
          "amount": "20.02",
          "formattedAmount": "20.02 €",
          "formattedValue": "20 %",
          "id": "v4-uuid-22",
          "kind": 1,
          "name": "Remise 20%",
          "quantity": "0",
          "value": 20,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 10,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 6.66,
          "formattedAmount": "6.66 €",
          "formattedTotalAmount": "66.60 €",
          "id": "v4-uuid-19",
          "kind": 2,
          "totalAmount": "66.6",
        },
        Object {
          "amount": 0.09,
          "formattedAmount": "0.09 €",
          "formattedTotalAmount": "0.90 €",
          "id": "v4-uuid-20",
          "kind": 1,
          "totalAmount": "0.9",
        },
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "320.10 €",
          "id": "v4-uuid-21",
          "kind": 0,
          "totalAmount": "320.1",
        },
      ],
      "formattedExpectedQuantity": "10",
      "formattedQuantity": "10",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
      "formattedTotalAmountExcludingTaxes": "464.96 €",
      "formattedTotalAmountIncludingTaxes": "557.95 €",
      "formattedTotalDiscounts": "22.74 €",
      "formattedTotalFees": "387.60 €",
      "formattedTotalLocalDiscounts": "20.02 €",
      "formattedTotalPrice": "100.10 €",
      "formattedUnitFee": "38.76 €",
      "formattedUnitPrice": "10.01 €",
      "id": "v4-uuid-23",
      "identifier": "v4-uuid-3",
      "name": "Product 3",
      "packaging": 7,
      "quantity": 10,
      "stock": 3,
      "stockKeepingUnit": "1479427200003",
      "taxes": Array [
        Object {
          "amount": "92.99",
          "formattedAmount": "92.99 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "467.68",
      "totalAmountExcludingTaxes": "464.96",
      "totalAmountIncludingTaxes": "557.95",
      "totalDiscounts": "22.74",
      "totalFees": "387.6",
      "totalGlobalDiscounts": "2.72",
      "totalLocalDiscounts": "20.02",
      "totalPrice": "100.1",
      "totalTaxes": "92.99",
      "totalTaxesExcludingGlobalDiscount": "93.54",
      "unitCost": "46.5",
      "unitFee": "38.76",
      "unitPrice": 10.01,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
      "formattedTotalAmountExcludingTaxes": "10.50 €",
      "formattedTotalAmountIncludingTaxes": "10.72 €",
      "formattedTotalDiscounts": "0.06 €",
      "formattedTotalFees": "9.99 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.57 €",
      "formattedUnitFee": "3.33 €",
      "formattedUnitPrice": "0.19 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "0.22",
          "formattedAmount": "0.22 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "10.56",
      "totalAmountExcludingTaxes": "10.5",
      "totalAmountIncludingTaxes": "10.72",
      "totalDiscounts": "0.06",
      "totalFees": "9.99",
      "totalGlobalDiscounts": "0.06",
      "totalLocalDiscounts": "0",
      "totalPrice": "0.57",
      "totalTaxes": "0.22",
      "totalTaxesExcludingGlobalDiscount": "0.22",
      "unitCost": "3.5",
      "unitFee": "3.33",
      "unitPrice": 0.19,
    },
  },
]
`;

exports[`reducer BatchProductAdded should match snapshot on second bulk product added 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        1,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 8",
      "discounts": Array [
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "formattedValue": "6.66 €",
          "id": "v4-uuid-39",
          "kind": 0,
          "name": "Remise 6.66 currency",
          "quantity": "0",
          "value": 6.66,
          "warnings": Array [
            0,
          ],
        },
      ],
      "expectedQuantity": 0,
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": "0",
      "formattedQuantity": "0",
      "formattedStock": "-1",
      "formattedTotalAmountExcludingGlobalDiscounts": "0.00 €",
      "formattedTotalAmountExcludingTaxes": "0.00 €",
      "formattedTotalAmountIncludingTaxes": "0.00 €",
      "formattedTotalDiscounts": "0.00 €",
      "formattedTotalFees": "0.00 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.00 €",
      "formattedUnitFee": "0.00 €",
      "formattedUnitPrice": "20.13 €",
      "id": "v4-uuid-40",
      "identifier": "v4-uuid-5",
      "name": "Product 8",
      "packaging": 1,
      "quantity": 0,
      "stock": -1,
      "stockKeepingUnit": "1479427200005",
      "taxes": Array [
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "0",
      "totalAmountExcludingTaxes": "0",
      "totalAmountIncludingTaxes": "0",
      "totalDiscounts": "0",
      "totalFees": "0",
      "totalGlobalDiscounts": "0",
      "totalLocalDiscounts": "0",
      "totalPrice": "0",
      "totalTaxes": "0",
      "totalTaxesExcludingGlobalDiscount": "0",
      "unitCost": undefined,
      "unitFee": "0",
      "unitPrice": 20.1281,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        1,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 9",
      "discounts": Array [
        Object {
          "amount": "8.71",
          "formattedAmount": "8.71 €",
          "formattedValue": "3.33 %",
          "id": "v4-uuid-35",
          "kind": 1,
          "name": "Remise 3.33%",
          "quantity": "0",
          "value": 3.33,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 13,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "416.13 €",
          "id": "v4-uuid-34",
          "kind": 0,
          "totalAmount": "416.13",
        },
      ],
      "formattedExpectedQuantity": "13",
      "formattedQuantity": "13",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "668.85 €",
      "formattedTotalAmountExcludingTaxes": "664.97 €",
      "formattedTotalAmountIncludingTaxes": "773.08 €",
      "formattedTotalDiscounts": "12.59 €",
      "formattedTotalFees": "416.13 €",
      "formattedTotalLocalDiscounts": "8.71 €",
      "formattedTotalPrice": "261.43 €",
      "formattedUnitFee": "32.01 €",
      "formattedUnitPrice": "20.11 €",
      "id": "v4-uuid-36",
      "identifier": "v4-uuid-6",
      "name": "Product 9",
      "packaging": 3,
      "quantity": 13,
      "stock": 3,
      "stockKeepingUnit": "1479427200006",
      "taxes": Array [
        Object {
          "amount": "24.88",
          "formattedAmount": "24.88 €",
          "rate": 10,
        },
        Object {
          "amount": "83.23",
          "formattedAmount": "83.23 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "668.85",
      "totalAmountExcludingTaxes": "664.97",
      "totalAmountIncludingTaxes": "773.08",
      "totalDiscounts": "12.59",
      "totalFees": "416.13",
      "totalGlobalDiscounts": "3.88",
      "totalLocalDiscounts": "8.71",
      "totalPrice": "261.43",
      "totalTaxes": "108.11",
      "totalTaxesExcludingGlobalDiscount": "108.5",
      "unitCost": "51.15",
      "unitFee": "32.01",
      "unitPrice": 20.11,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [],
      "capacityUnit": undefined,
      "description": "Description of product 3",
      "discounts": Array [
        Object {
          "amount": "20.02",
          "formattedAmount": "20.02 €",
          "formattedValue": "20 %",
          "id": "v4-uuid-22",
          "kind": 1,
          "name": "Remise 20%",
          "quantity": "0",
          "value": 20,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 10,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 6.66,
          "formattedAmount": "6.66 €",
          "formattedTotalAmount": "66.60 €",
          "id": "v4-uuid-19",
          "kind": 2,
          "totalAmount": "66.6",
        },
        Object {
          "amount": 0.09,
          "formattedAmount": "0.09 €",
          "formattedTotalAmount": "0.90 €",
          "id": "v4-uuid-20",
          "kind": 1,
          "totalAmount": "0.9",
        },
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "320.10 €",
          "id": "v4-uuid-21",
          "kind": 0,
          "totalAmount": "320.1",
        },
      ],
      "formattedExpectedQuantity": "10",
      "formattedQuantity": "10",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
      "formattedTotalAmountExcludingTaxes": "464.96 €",
      "formattedTotalAmountIncludingTaxes": "557.95 €",
      "formattedTotalDiscounts": "22.74 €",
      "formattedTotalFees": "387.60 €",
      "formattedTotalLocalDiscounts": "20.02 €",
      "formattedTotalPrice": "100.10 €",
      "formattedUnitFee": "38.76 €",
      "formattedUnitPrice": "10.01 €",
      "id": "v4-uuid-23",
      "identifier": "v4-uuid-3",
      "name": "Product 3",
      "packaging": 7,
      "quantity": 10,
      "stock": 3,
      "stockKeepingUnit": "1479427200003",
      "taxes": Array [
        Object {
          "amount": "92.99",
          "formattedAmount": "92.99 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "467.68",
      "totalAmountExcludingTaxes": "464.96",
      "totalAmountIncludingTaxes": "557.95",
      "totalDiscounts": "22.74",
      "totalFees": "387.6",
      "totalGlobalDiscounts": "2.72",
      "totalLocalDiscounts": "20.02",
      "totalPrice": "100.1",
      "totalTaxes": "92.99",
      "totalTaxesExcludingGlobalDiscount": "93.54",
      "unitCost": "46.5",
      "unitFee": "38.76",
      "unitPrice": 10.01,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
      "formattedTotalAmountExcludingTaxes": "10.50 €",
      "formattedTotalAmountIncludingTaxes": "10.72 €",
      "formattedTotalDiscounts": "0.06 €",
      "formattedTotalFees": "9.99 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.57 €",
      "formattedUnitFee": "3.33 €",
      "formattedUnitPrice": "0.19 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "0.22",
          "formattedAmount": "0.22 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "10.56",
      "totalAmountExcludingTaxes": "10.5",
      "totalAmountIncludingTaxes": "10.72",
      "totalDiscounts": "0.06",
      "totalFees": "9.99",
      "totalGlobalDiscounts": "0.06",
      "totalLocalDiscounts": "0",
      "totalPrice": "0.57",
      "totalTaxes": "0.22",
      "totalTaxesExcludingGlobalDiscount": "0.22",
      "unitCost": "3.5",
      "unitFee": "3.33",
      "unitPrice": 0.19,
    },
  },
]
`;

exports[`reducer GlobalDiscountAdded should apply a global discount to the cart 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
    Object {
      "amount": "2.69",
      "formattedAmount": "2.69 €",
      "formattedValue": "3.33 %",
      "id": "v4-uuid-46",
      "kind": 1,
      "name": "Remise 3.33%",
      "quantity": "0",
      "value": 3.33,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.24 €",
  "formattedTotalAmountExcludingTaxes": "468.89 €",
  "formattedTotalAmountIncludingTaxes": "560.82 €",
  "formattedTotalAmountOfGoods": "71.30 €",
  "formattedTotalTaxes": "91.93 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "458.54 €",
        "formattedTotalAmountIncludingTaxes": "550.25 €",
        "formattedTotalDiscounts": "29.16 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "91.71",
            "formattedAmount": "91.71 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "458.54",
        "totalAmountIncludingTaxes": "550.25",
        "totalDiscounts": "29.16",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "9.14",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "91.71",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "45.85",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.35 €",
        "formattedTotalAmountIncludingTaxes": "10.57 €",
        "formattedTotalDiscounts": "0.21 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.35",
        "totalAmountIncludingTaxes": "10.57",
        "totalDiscounts": "0.21",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.21",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.45",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "91.71",
      "formattedAmount": "91.71 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.24",
  "totalAmountExcludingTaxes": "468.89",
  "totalAmountIncludingTaxes": "560.82",
  "totalAmountOfGoods": "71.3",
  "totalDiscounts": "29.37",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "91.93",
}
`;

exports[`reducer GlobalDiscountRemoved should remove a discount from a cart and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.24 €",
  "formattedTotalAmountExcludingTaxes": "478.24 €",
  "formattedTotalAmountIncludingTaxes": "572.00 €",
  "formattedTotalAmountOfGoods": "80.65 €",
  "formattedTotalTaxes": "93.76 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "467.68 €",
        "formattedTotalAmountIncludingTaxes": "561.22 €",
        "formattedTotalDiscounts": "20.02 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "93.54",
            "formattedAmount": "93.54 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "467.68",
        "totalAmountIncludingTaxes": "561.22",
        "totalDiscounts": "20.02",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "93.54",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.77",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.56 €",
        "formattedTotalAmountIncludingTaxes": "10.78 €",
        "formattedTotalDiscounts": "0.00 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.56",
        "totalAmountIncludingTaxes": "10.78",
        "totalDiscounts": "0",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.52",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "93.54",
      "formattedAmount": "93.54 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.24",
  "totalAmountExcludingTaxes": "478.24",
  "totalAmountIncludingTaxes": "572",
  "totalAmountOfGoods": "80.65",
  "totalDiscounts": "20.02",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "93.76",
}
`;

exports[`reducer GlobalDiscountUpdated should not update any discount in a cart because the discount.id provided doesnt exists 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.24 €",
  "formattedTotalAmountExcludingTaxes": "471.58 €",
  "formattedTotalAmountIncludingTaxes": "564.03 €",
  "formattedTotalAmountOfGoods": "73.99 €",
  "formattedTotalTaxes": "92.45 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.17 €",
        "formattedTotalAmountIncludingTaxes": "553.40 €",
        "formattedTotalDiscounts": "26.53 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "92.23",
            "formattedAmount": "92.23 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.17",
        "totalAmountIncludingTaxes": "553.4",
        "totalDiscounts": "26.53",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.51",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "92.23",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.41 €",
        "formattedTotalAmountIncludingTaxes": "10.63 €",
        "formattedTotalDiscounts": "0.15 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.41",
        "totalAmountIncludingTaxes": "10.63",
        "totalDiscounts": "0.15",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.15",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.47",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "92.23",
      "formattedAmount": "92.23 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.24",
  "totalAmountExcludingTaxes": "471.58",
  "totalAmountIncludingTaxes": "564.03",
  "totalAmountOfGoods": "73.99",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "92.45",
}
`;

exports[`reducer GlobalDiscountUpdated should update a discount in a cart and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "16.13",
      "formattedAmount": "16.13 €",
      "formattedValue": "20 %",
      "id": "v4-uuid-26",
      "kind": 1,
      "name": "Remise 20%",
      "quantity": "0",
      "value": 20,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.24 €",
  "formattedTotalAmountExcludingTaxes": "462.11 €",
  "formattedTotalAmountIncludingTaxes": "552.70 €",
  "formattedTotalAmountOfGoods": "64.52 €",
  "formattedTotalTaxes": "90.59 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "451.91 €",
        "formattedTotalAmountIncludingTaxes": "542.29 €",
        "formattedTotalDiscounts": "35.79 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "90.38",
            "formattedAmount": "90.38 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "451.91",
        "totalAmountIncludingTaxes": "542.29",
        "totalDiscounts": "35.79",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "15.77",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "90.38",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "45.19",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.20 €",
        "formattedTotalAmountIncludingTaxes": "10.41 €",
        "formattedTotalDiscounts": "0.36 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.21",
            "formattedAmount": "0.21 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.2",
        "totalAmountIncludingTaxes": "10.41",
        "totalDiscounts": "0.36",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.36",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.21",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.4",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.21",
      "formattedAmount": "0.21 €",
      "rate": 2.1,
    },
    Object {
      "amount": "90.38",
      "formattedAmount": "90.38 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.24",
  "totalAmountExcludingTaxes": "462.11",
  "totalAmountIncludingTaxes": "552.7",
  "totalAmountOfGoods": "64.52",
  "totalDiscounts": "36.15",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "90.59",
}
`;

exports[`reducer ProductAdded should match snapshot 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        1,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 8",
      "discounts": Array [
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "formattedValue": "6.66 €",
          "id": "v4-uuid-27",
          "kind": 0,
          "name": "Remise 6.66 currency",
          "quantity": "0",
          "value": 6.66,
          "warnings": Array [
            0,
          ],
        },
      ],
      "expectedQuantity": 0,
      "expectedQuantityWarning": Array [],
      "fees": Array [],
      "formattedExpectedQuantity": "0",
      "formattedQuantity": "0",
      "formattedStock": "-1",
      "formattedTotalAmountExcludingGlobalDiscounts": "0.00 €",
      "formattedTotalAmountExcludingTaxes": "0.00 €",
      "formattedTotalAmountIncludingTaxes": "0.00 €",
      "formattedTotalDiscounts": "0.00 €",
      "formattedTotalFees": "0.00 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.00 €",
      "formattedUnitFee": "0.00 €",
      "formattedUnitPrice": "20.13 €",
      "id": "v4-uuid-28",
      "identifier": "v4-uuid-5",
      "name": "Product 8",
      "packaging": 1,
      "quantity": 0,
      "stock": -1,
      "stockKeepingUnit": "1479427200005",
      "taxes": Array [
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "0",
      "totalAmountExcludingTaxes": "0",
      "totalAmountIncludingTaxes": "0",
      "totalDiscounts": "0",
      "totalFees": "0",
      "totalGlobalDiscounts": "0",
      "totalLocalDiscounts": "0",
      "totalPrice": "0",
      "totalTaxes": "0",
      "totalTaxesExcludingGlobalDiscount": "0",
      "unitCost": undefined,
      "unitFee": "0",
      "unitPrice": 20.1281,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [],
      "capacityUnit": undefined,
      "description": "Description of product 3",
      "discounts": Array [
        Object {
          "amount": "20.02",
          "formattedAmount": "20.02 €",
          "formattedValue": "20 %",
          "id": "v4-uuid-22",
          "kind": 1,
          "name": "Remise 20%",
          "quantity": "0",
          "value": 20,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 10,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 6.66,
          "formattedAmount": "6.66 €",
          "formattedTotalAmount": "66.60 €",
          "id": "v4-uuid-19",
          "kind": 2,
          "totalAmount": "66.6",
        },
        Object {
          "amount": 0.09,
          "formattedAmount": "0.09 €",
          "formattedTotalAmount": "0.90 €",
          "id": "v4-uuid-20",
          "kind": 1,
          "totalAmount": "0.9",
        },
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "320.10 €",
          "id": "v4-uuid-21",
          "kind": 0,
          "totalAmount": "320.1",
        },
      ],
      "formattedExpectedQuantity": "10",
      "formattedQuantity": "10",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
      "formattedTotalAmountExcludingTaxes": "461.17 €",
      "formattedTotalAmountIncludingTaxes": "553.40 €",
      "formattedTotalDiscounts": "26.53 €",
      "formattedTotalFees": "387.60 €",
      "formattedTotalLocalDiscounts": "20.02 €",
      "formattedTotalPrice": "100.10 €",
      "formattedUnitFee": "38.76 €",
      "formattedUnitPrice": "10.01 €",
      "id": "v4-uuid-23",
      "identifier": "v4-uuid-3",
      "name": "Product 3",
      "packaging": 7,
      "quantity": 10,
      "stock": 3,
      "stockKeepingUnit": "1479427200003",
      "taxes": Array [
        Object {
          "amount": "92.23",
          "formattedAmount": "92.23 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "467.68",
      "totalAmountExcludingTaxes": "461.17",
      "totalAmountIncludingTaxes": "553.4",
      "totalDiscounts": "26.53",
      "totalFees": "387.6",
      "totalGlobalDiscounts": "6.51",
      "totalLocalDiscounts": "20.02",
      "totalPrice": "100.1",
      "totalTaxes": "92.23",
      "totalTaxesExcludingGlobalDiscount": "93.54",
      "unitCost": "46.12",
      "unitFee": "38.76",
      "unitPrice": 10.01,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
      "formattedTotalAmountExcludingTaxes": "10.41 €",
      "formattedTotalAmountIncludingTaxes": "10.63 €",
      "formattedTotalDiscounts": "0.15 €",
      "formattedTotalFees": "9.99 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.57 €",
      "formattedUnitFee": "3.33 €",
      "formattedUnitPrice": "0.19 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "0.22",
          "formattedAmount": "0.22 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "10.56",
      "totalAmountExcludingTaxes": "10.41",
      "totalAmountIncludingTaxes": "10.63",
      "totalDiscounts": "0.15",
      "totalFees": "9.99",
      "totalGlobalDiscounts": "0.15",
      "totalLocalDiscounts": "0",
      "totalPrice": "0.57",
      "totalTaxes": "0.22",
      "totalTaxesExcludingGlobalDiscount": "0.22",
      "unitCost": "3.47",
      "unitFee": "3.33",
      "unitPrice": 0.19,
    },
  },
]
`;

exports[`reducer ProductDiscountAdded should match snapshot after applying currency discount 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.23 €",
  "formattedTotalAmountExcludingTaxes": "471.57 €",
  "formattedTotalAmountIncludingTaxes": "564.02 €",
  "formattedTotalAmountOfGoods": "73.98 €",
  "formattedTotalTaxes": "92.45 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.17 €",
        "formattedTotalAmountIncludingTaxes": "553.40 €",
        "formattedTotalDiscounts": "26.53 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "92.23",
            "formattedAmount": "92.23 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.17",
        "totalAmountIncludingTaxes": "553.4",
        "totalDiscounts": "26.53",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.51",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "92.23",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [
          Object {
            "amount": "0.01",
            "formattedAmount": "0.01 €",
            "formattedValue": "0.01 €",
            "id": "v4-uuid-86",
            "kind": 0,
            "name": "Remise 0.01 currency",
            "quantity": "3",
            "value": 0.01,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.55 €",
        "formattedTotalAmountExcludingTaxes": "10.40 €",
        "formattedTotalAmountIncludingTaxes": "10.62 €",
        "formattedTotalDiscounts": "0.16 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.01 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.55",
        "totalAmountExcludingTaxes": "10.4",
        "totalAmountIncludingTaxes": "10.62",
        "totalDiscounts": "0.16",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.15",
        "totalLocalDiscounts": "0.01",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.47",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "92.23",
      "formattedAmount": "92.23 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.23",
  "totalAmountExcludingTaxes": "471.57",
  "totalAmountIncludingTaxes": "564.02",
  "totalAmountOfGoods": "73.98",
  "totalDiscounts": "26.69",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "92.45",
}
`;

exports[`reducer ProductDiscountAdded should match snapshot after applying percent discount 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.22 €",
  "formattedTotalAmountExcludingTaxes": "471.56 €",
  "formattedTotalAmountIncludingTaxes": "564.01 €",
  "formattedTotalAmountOfGoods": "73.97 €",
  "formattedTotalTaxes": "92.45 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.17 €",
        "formattedTotalAmountIncludingTaxes": "553.40 €",
        "formattedTotalDiscounts": "26.53 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "92.23",
            "formattedAmount": "92.23 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.17",
        "totalAmountIncludingTaxes": "553.4",
        "totalDiscounts": "26.53",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.51",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "92.23",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [
          Object {
            "amount": "0.02",
            "formattedAmount": "0.02 €",
            "formattedValue": "3.33 %",
            "id": "v4-uuid-85",
            "kind": 1,
            "name": "Remise 3.33%",
            "quantity": "3",
            "value": 3.33,
            "warnings": Array [
              0,
            ],
          },
        ],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.54 €",
        "formattedTotalAmountExcludingTaxes": "10.39 €",
        "formattedTotalAmountIncludingTaxes": "10.61 €",
        "formattedTotalDiscounts": "0.17 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.02 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.54",
        "totalAmountExcludingTaxes": "10.39",
        "totalAmountIncludingTaxes": "10.61",
        "totalDiscounts": "0.17",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.15",
        "totalLocalDiscounts": "0.02",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.46",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "92.23",
      "formattedAmount": "92.23 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.22",
  "totalAmountExcludingTaxes": "471.56",
  "totalAmountIncludingTaxes": "564.01",
  "totalAmountOfGoods": "73.97",
  "totalDiscounts": "26.7",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "92.45",
}
`;

exports[`reducer ProductDiscountRemoved should remove a discount from a product and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "498.26 €",
  "formattedTotalAmountExcludingTaxes": "491.60 €",
  "formattedTotalAmountIncludingTaxes": "588.06 €",
  "formattedTotalAmountOfGoods": "94.01 €",
  "formattedTotalTaxes": "96.46 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "487.70 €",
        "formattedTotalAmountExcludingTaxes": "481.18 €",
        "formattedTotalAmountIncludingTaxes": "577.42 €",
        "formattedTotalDiscounts": "6.52 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "96.24",
            "formattedAmount": "96.24 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "487.7",
        "totalAmountExcludingTaxes": "481.18",
        "totalAmountIncludingTaxes": "577.42",
        "totalDiscounts": "6.52",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.52",
        "totalLocalDiscounts": "0",
        "totalPrice": "100.1",
        "totalTaxes": "96.24",
        "totalTaxesExcludingGlobalDiscount": "97.54",
        "unitCost": "48.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.42 €",
        "formattedTotalAmountIncludingTaxes": "10.64 €",
        "formattedTotalDiscounts": "0.14 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.42",
        "totalAmountIncludingTaxes": "10.64",
        "totalDiscounts": "0.14",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.14",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.47",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "96.24",
      "formattedAmount": "96.24 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "498.26",
  "totalAmountExcludingTaxes": "491.6",
  "totalAmountIncludingTaxes": "588.06",
  "totalAmountOfGoods": "94.01",
  "totalDiscounts": "6.66",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "96.46",
}
`;

exports[`reducer ProductDiscountUpdated should update a discount and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "498.26 €",
  "formattedTotalAmountExcludingTaxes": "491.60 €",
  "formattedTotalAmountIncludingTaxes": "588.06 €",
  "formattedTotalAmountOfGoods": "94.01 €",
  "formattedTotalTaxes": "96.46 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "formattedValue": undefined,
            "id": "v4-uuid-22",
            "kind": 2,
            "name": "Remise de 0 gratuit",
            "quantity": "0",
            "value": 0,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "487.70 €",
        "formattedTotalAmountExcludingTaxes": "481.18 €",
        "formattedTotalAmountIncludingTaxes": "577.42 €",
        "formattedTotalDiscounts": "6.52 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "96.24",
            "formattedAmount": "96.24 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "487.7",
        "totalAmountExcludingTaxes": "481.18",
        "totalAmountIncludingTaxes": "577.42",
        "totalDiscounts": "6.52",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.52",
        "totalLocalDiscounts": "0",
        "totalPrice": "100.1",
        "totalTaxes": "96.24",
        "totalTaxesExcludingGlobalDiscount": "97.54",
        "unitCost": "48.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.42 €",
        "formattedTotalAmountIncludingTaxes": "10.64 €",
        "formattedTotalDiscounts": "0.14 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.42",
        "totalAmountIncludingTaxes": "10.64",
        "totalDiscounts": "0.14",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.14",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.47",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "96.24",
      "formattedAmount": "96.24 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "498.26",
  "totalAmountExcludingTaxes": "491.6",
  "totalAmountIncludingTaxes": "588.06",
  "totalAmountOfGoods": "94.01",
  "totalDiscounts": "6.66",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "96.46",
}
`;

exports[`reducer ProductExpectedQuantityUpdated should match snapshot 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [],
      "capacityUnit": undefined,
      "description": "Description of product 3",
      "discounts": Array [
        Object {
          "amount": "6.01",
          "formattedAmount": "6.01 €",
          "formattedValue": "20 %",
          "id": "v4-uuid-22",
          "kind": 1,
          "name": "Remise 20%",
          "quantity": "0",
          "value": 20,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 6.66,
          "formattedAmount": "6.66 €",
          "formattedTotalAmount": "19.98 €",
          "id": "v4-uuid-19",
          "kind": 2,
          "totalAmount": "19.98",
        },
        Object {
          "amount": 0.09,
          "formattedAmount": "0.09 €",
          "formattedTotalAmount": "0.27 €",
          "id": "v4-uuid-20",
          "kind": 1,
          "totalAmount": "0.27",
        },
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "96.03 €",
          "id": "v4-uuid-21",
          "kind": 0,
          "totalAmount": "96.03",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "140.30 €",
      "formattedTotalAmountExcludingTaxes": "134.11 €",
      "formattedTotalAmountIncludingTaxes": "160.94 €",
      "formattedTotalDiscounts": "12.20 €",
      "formattedTotalFees": "116.28 €",
      "formattedTotalLocalDiscounts": "6.01 €",
      "formattedTotalPrice": "30.03 €",
      "formattedUnitFee": "38.76 €",
      "formattedUnitPrice": "10.01 €",
      "id": "v4-uuid-23",
      "identifier": "v4-uuid-3",
      "name": "Product 3",
      "packaging": 7,
      "quantity": 3,
      "stock": 3,
      "stockKeepingUnit": "1479427200003",
      "taxes": Array [
        Object {
          "amount": "26.83",
          "formattedAmount": "26.83 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "140.3",
      "totalAmountExcludingTaxes": "134.11",
      "totalAmountIncludingTaxes": "160.94",
      "totalDiscounts": "12.2",
      "totalFees": "116.28",
      "totalGlobalDiscounts": "6.19",
      "totalLocalDiscounts": "6.01",
      "totalPrice": "30.03",
      "totalTaxes": "26.83",
      "totalTaxesExcludingGlobalDiscount": "28.06",
      "unitCost": "44.7",
      "unitFee": "38.76",
      "unitPrice": 10.01,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
      "formattedTotalAmountExcludingTaxes": "10.09 €",
      "formattedTotalAmountIncludingTaxes": "10.30 €",
      "formattedTotalDiscounts": "0.47 €",
      "formattedTotalFees": "9.99 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.57 €",
      "formattedUnitFee": "3.33 €",
      "formattedUnitPrice": "0.19 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "0.21",
          "formattedAmount": "0.21 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "10.56",
      "totalAmountExcludingTaxes": "10.09",
      "totalAmountIncludingTaxes": "10.3",
      "totalDiscounts": "0.47",
      "totalFees": "9.99",
      "totalGlobalDiscounts": "0.47",
      "totalLocalDiscounts": "0",
      "totalPrice": "0.57",
      "totalTaxes": "0.21",
      "totalTaxesExcludingGlobalDiscount": "0.22",
      "unitCost": "3.36",
      "unitFee": "3.33",
      "unitPrice": 0.19,
    },
  },
]
`;

exports[`reducer ProductFeeRemoved should remove a fee from a product and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "158.14 €",
  "formattedTotalAmountExcludingTaxes": "151.48 €",
  "formattedTotalAmountIncludingTaxes": "179.96 €",
  "formattedTotalAmountOfGoods": "73.99 €",
  "formattedTotalTaxes": "28.48 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "147.58 €",
        "formattedTotalAmountExcludingTaxes": "141.36 €",
        "formattedTotalAmountIncludingTaxes": "169.63 €",
        "formattedTotalDiscounts": "26.24 €",
        "formattedTotalFees": "67.50 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "6.75 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "28.27",
            "formattedAmount": "28.27 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "147.58",
        "totalAmountExcludingTaxes": "141.36",
        "totalAmountIncludingTaxes": "169.63",
        "totalDiscounts": "26.24",
        "totalFees": "67.5",
        "totalGlobalDiscounts": "6.22",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "28.27",
        "totalTaxesExcludingGlobalDiscount": "29.52",
        "unitCost": "14.14",
        "unitFee": "6.75",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.12 €",
        "formattedTotalAmountIncludingTaxes": "10.33 €",
        "formattedTotalDiscounts": "0.44 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.21",
            "formattedAmount": "0.21 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.12",
        "totalAmountIncludingTaxes": "10.33",
        "totalDiscounts": "0.44",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.44",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.21",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.37",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.21",
      "formattedAmount": "0.21 €",
      "rate": 2.1,
    },
    Object {
      "amount": "28.27",
      "formattedAmount": "28.27 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "158.14",
  "totalAmountExcludingTaxes": "151.48",
  "totalAmountIncludingTaxes": "179.96",
  "totalAmountOfGoods": "73.99",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "28.48",
}
`;

exports[`reducer ProductFeeReplicated should match snapshot 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [],
      "capacityUnit": undefined,
      "description": "Description of product 3",
      "discounts": Array [
        Object {
          "amount": "20.02",
          "formattedAmount": "20.02 €",
          "formattedValue": "20 %",
          "id": "v4-uuid-22",
          "kind": 1,
          "name": "Remise 20%",
          "quantity": "0",
          "value": 20,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 10,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 0.01,
          "formattedAmount": "0.01 €",
          "formattedTotalAmount": "0.10 €",
          "id": "v4-uuid-19",
          "kind": 2,
          "totalAmount": "0.1",
        },
        Object {
          "amount": 0.09,
          "formattedAmount": "0.09 €",
          "formattedTotalAmount": "0.90 €",
          "id": "v4-uuid-20",
          "kind": 1,
          "totalAmount": "0.9",
        },
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "320.10 €",
          "id": "v4-uuid-21",
          "kind": 0,
          "totalAmount": "320.1",
        },
      ],
      "formattedExpectedQuantity": "10",
      "formattedQuantity": "10",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "401.18 €",
      "formattedTotalAmountExcludingTaxes": "394.69 €",
      "formattedTotalAmountIncludingTaxes": "473.63 €",
      "formattedTotalDiscounts": "26.51 €",
      "formattedTotalFees": "321.10 €",
      "formattedTotalLocalDiscounts": "20.02 €",
      "formattedTotalPrice": "100.10 €",
      "formattedUnitFee": "32.11 €",
      "formattedUnitPrice": "10.01 €",
      "id": "v4-uuid-23",
      "identifier": "v4-uuid-3",
      "name": "Product 3",
      "packaging": 7,
      "quantity": 10,
      "stock": 3,
      "stockKeepingUnit": "1479427200003",
      "taxes": Array [
        Object {
          "amount": "78.94",
          "formattedAmount": "78.94 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "401.18",
      "totalAmountExcludingTaxes": "394.69",
      "totalAmountIncludingTaxes": "473.63",
      "totalDiscounts": "26.51",
      "totalFees": "321.1",
      "totalGlobalDiscounts": "6.49",
      "totalLocalDiscounts": "20.02",
      "totalPrice": "100.1",
      "totalTaxes": "78.94",
      "totalTaxesExcludingGlobalDiscount": "80.24",
      "unitCost": "39.47",
      "unitFee": "32.11",
      "unitPrice": 10.01,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
        Object {
          "amount": 0.01,
          "formattedAmount": "0.01 €",
          "formattedTotalAmount": "0.03 €",
          "id": "v4-uuid-43",
          "kind": 2,
          "totalAmount": "0.03",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "10.59 €",
      "formattedTotalAmountExcludingTaxes": "10.42 €",
      "formattedTotalAmountIncludingTaxes": "10.64 €",
      "formattedTotalDiscounts": "0.17 €",
      "formattedTotalFees": "10.02 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.57 €",
      "formattedUnitFee": "3.34 €",
      "formattedUnitPrice": "0.19 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "0.22",
          "formattedAmount": "0.22 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "10.59",
      "totalAmountExcludingTaxes": "10.42",
      "totalAmountIncludingTaxes": "10.64",
      "totalDiscounts": "0.17",
      "totalFees": "10.02",
      "totalGlobalDiscounts": "0.17",
      "totalLocalDiscounts": "0",
      "totalPrice": "0.57",
      "totalTaxes": "0.22",
      "totalTaxesExcludingGlobalDiscount": "0.22",
      "unitCost": "3.47",
      "unitFee": "3.34",
      "unitPrice": 0.19,
    },
  },
]
`;

exports[`reducer ProductFeeUpdated should update a fee and match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "322.89",
      "formattedAmount": "322.89 €",
      "kind": 0,
    },
    Object {
      "amount": "0.9",
      "formattedAmount": "0.90 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "471.04 €",
  "formattedTotalAmountExcludingTaxes": "464.38 €",
  "formattedTotalAmountIncludingTaxes": "557.16 €",
  "formattedTotalAmountOfGoods": "73.99 €",
  "formattedTotalTaxes": "92.78 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.07 €",
        "formattedTotalAmountIncludingTaxes": "553.28 €",
        "formattedTotalDiscounts": "26.63 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "92.21",
            "formattedAmount": "92.21 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.07",
        "totalAmountIncludingTaxes": "553.28",
        "totalDiscounts": "26.63",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.61",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "92.21",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.11",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          1,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 0.93,
            "formattedAmount": "0.93 €",
            "formattedTotalAmount": "2.79 €",
            "id": "v4-uuid-24",
            "kind": 0,
            "totalAmount": "2.79",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "3.36 €",
        "formattedTotalAmountExcludingTaxes": "3.31 €",
        "formattedTotalAmountIncludingTaxes": "3.88 €",
        "formattedTotalDiscounts": "0.05 €",
        "formattedTotalFees": "2.79 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "0.93 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.01",
            "formattedAmount": "0.01 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0.56",
            "formattedAmount": "0.56 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "3.36",
        "totalAmountExcludingTaxes": "3.31",
        "totalAmountIncludingTaxes": "3.88",
        "totalDiscounts": "0.05",
        "totalFees": "2.79",
        "totalGlobalDiscounts": "0.05",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.57",
        "totalTaxesExcludingGlobalDiscount": "0.57",
        "unitCost": "1.1",
        "unitFee": "0.93",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.01",
      "formattedAmount": "0.01 €",
      "rate": 2.1,
    },
    Object {
      "amount": "92.77",
      "formattedAmount": "92.77 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "471.04",
  "totalAmountExcludingTaxes": "464.38",
  "totalAmountIncludingTaxes": "557.16",
  "totalAmountOfGoods": "73.99",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "92.78",
}
`;

exports[`reducer ProductNameUpdated, ProductDescriptionUpdated should match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": undefined,
      "formattedValue": undefined,
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": undefined,
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": undefined,
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": undefined,
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": undefined,
  "formattedTotalAmountExcludingTaxes": undefined,
  "formattedTotalAmountIncludingTaxes": undefined,
  "formattedTotalAmountOfGoods": undefined,
  "formattedTotalTaxes": undefined,
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": undefined,
            "formattedValue": undefined,
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "92.23",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.17",
        "totalAmountIncludingTaxes": "553.4",
        "totalDiscounts": "26.53",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.51",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "92.23",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "This is the final description for product C",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "A new name for product C",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": undefined,
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.41",
        "totalAmountIncludingTaxes": "10.63",
        "totalDiscounts": "0.15",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.15",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.47",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": undefined,
      "rate": 2.1,
    },
    Object {
      "amount": "92.23",
      "formattedAmount": undefined,
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.24",
  "totalAmountExcludingTaxes": "471.58",
  "totalAmountIncludingTaxes": "564.03",
  "totalAmountOfGoods": "73.99",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "92.45",
}
`;

exports[`reducer ProductQuantityUpdated should match snapshot 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [],
      "capacityUnit": undefined,
      "description": "Description of product 3",
      "discounts": Array [
        Object {
          "amount": "6.01",
          "formattedAmount": "6.01 €",
          "formattedValue": "20 %",
          "id": "v4-uuid-22",
          "kind": 1,
          "name": "Remise 20%",
          "quantity": "0",
          "value": 20,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 10,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 6.66,
          "formattedAmount": "6.66 €",
          "formattedTotalAmount": "19.98 €",
          "id": "v4-uuid-19",
          "kind": 2,
          "totalAmount": "19.98",
        },
        Object {
          "amount": 0.09,
          "formattedAmount": "0.09 €",
          "formattedTotalAmount": "0.27 €",
          "id": "v4-uuid-20",
          "kind": 1,
          "totalAmount": "0.27",
        },
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "96.03 €",
          "id": "v4-uuid-21",
          "kind": 0,
          "totalAmount": "96.03",
        },
      ],
      "formattedExpectedQuantity": "10",
      "formattedQuantity": "3",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "140.30 €",
      "formattedTotalAmountExcludingTaxes": "134.11 €",
      "formattedTotalAmountIncludingTaxes": "160.94 €",
      "formattedTotalDiscounts": "12.20 €",
      "formattedTotalFees": "116.28 €",
      "formattedTotalLocalDiscounts": "6.01 €",
      "formattedTotalPrice": "30.03 €",
      "formattedUnitFee": "38.76 €",
      "formattedUnitPrice": "10.01 €",
      "id": "v4-uuid-23",
      "identifier": "v4-uuid-3",
      "name": "Product 3",
      "packaging": 7,
      "quantity": 3,
      "stock": 3,
      "stockKeepingUnit": "1479427200003",
      "taxes": Array [
        Object {
          "amount": "26.83",
          "formattedAmount": "26.83 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "140.3",
      "totalAmountExcludingTaxes": "134.11",
      "totalAmountIncludingTaxes": "160.94",
      "totalDiscounts": "12.2",
      "totalFees": "116.28",
      "totalGlobalDiscounts": "6.19",
      "totalLocalDiscounts": "6.01",
      "totalPrice": "30.03",
      "totalTaxes": "26.83",
      "totalTaxesExcludingGlobalDiscount": "28.06",
      "unitCost": "44.7",
      "unitFee": "38.76",
      "unitPrice": 10.01,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
      "formattedTotalAmountExcludingTaxes": "10.09 €",
      "formattedTotalAmountIncludingTaxes": "10.30 €",
      "formattedTotalDiscounts": "0.47 €",
      "formattedTotalFees": "9.99 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.57 €",
      "formattedUnitFee": "3.33 €",
      "formattedUnitPrice": "0.19 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "0.21",
          "formattedAmount": "0.21 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "10.56",
      "totalAmountExcludingTaxes": "10.09",
      "totalAmountIncludingTaxes": "10.3",
      "totalDiscounts": "0.47",
      "totalFees": "9.99",
      "totalGlobalDiscounts": "0.47",
      "totalLocalDiscounts": "0",
      "totalPrice": "0.57",
      "totalTaxes": "0.21",
      "totalTaxesExcludingGlobalDiscount": "0.22",
      "unitCost": "3.36",
      "unitFee": "3.33",
      "unitPrice": 0.19,
    },
  },
]
`;

exports[`reducer ProductRemoved should match snapshot 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
      "formattedTotalAmountExcludingTaxes": "3.90 €",
      "formattedTotalAmountIncludingTaxes": "3.98 €",
      "formattedTotalDiscounts": "6.66 €",
      "formattedTotalFees": "9.99 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "0.57 €",
      "formattedUnitFee": "3.33 €",
      "formattedUnitPrice": "0.19 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "0.08",
          "formattedAmount": "0.08 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "10.56",
      "totalAmountExcludingTaxes": "3.9",
      "totalAmountIncludingTaxes": "3.98",
      "totalDiscounts": "6.66",
      "totalFees": "9.99",
      "totalGlobalDiscounts": "6.66",
      "totalLocalDiscounts": "0",
      "totalPrice": "0.57",
      "totalTaxes": "0.08",
      "totalTaxesExcludingGlobalDiscount": "0.22",
      "unitCost": "1.3",
      "unitFee": "3.33",
      "unitPrice": 0.19,
    },
  },
]
`;

exports[`reducer ProductUnitPriceUpdated should match snapshot 1`] = `
Array [
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [],
      "capacityUnit": undefined,
      "description": "Description of product 3",
      "discounts": Array [
        Object {
          "amount": "20.02",
          "formattedAmount": "20.02 €",
          "formattedValue": "20 %",
          "id": "v4-uuid-22",
          "kind": 1,
          "name": "Remise 20%",
          "quantity": "0",
          "value": 20,
          "warnings": Array [],
        },
      ],
      "expectedQuantity": 10,
      "expectedQuantityWarning": Array [
        0,
      ],
      "fees": Array [
        Object {
          "amount": 6.66,
          "formattedAmount": "6.66 €",
          "formattedTotalAmount": "66.60 €",
          "id": "v4-uuid-19",
          "kind": 2,
          "totalAmount": "66.6",
        },
        Object {
          "amount": 0.09,
          "formattedAmount": "0.09 €",
          "formattedTotalAmount": "0.90 €",
          "id": "v4-uuid-20",
          "kind": 1,
          "totalAmount": "0.9",
        },
        Object {
          "amount": 32.01,
          "formattedAmount": "32.01 €",
          "formattedTotalAmount": "320.10 €",
          "id": "v4-uuid-21",
          "kind": 0,
          "totalAmount": "320.1",
        },
      ],
      "formattedExpectedQuantity": "10",
      "formattedQuantity": "10",
      "formattedStock": "3",
      "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
      "formattedTotalAmountExcludingTaxes": "461.98 €",
      "formattedTotalAmountIncludingTaxes": "554.38 €",
      "formattedTotalDiscounts": "25.72 €",
      "formattedTotalFees": "387.60 €",
      "formattedTotalLocalDiscounts": "20.02 €",
      "formattedTotalPrice": "100.10 €",
      "formattedUnitFee": "38.76 €",
      "formattedUnitPrice": "10.01 €",
      "id": "v4-uuid-23",
      "identifier": "v4-uuid-3",
      "name": "Product 3",
      "packaging": 7,
      "quantity": 10,
      "stock": 3,
      "stockKeepingUnit": "1479427200003",
      "taxes": Array [
        Object {
          "amount": "92.4",
          "formattedAmount": "92.40 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "467.68",
      "totalAmountExcludingTaxes": "461.98",
      "totalAmountIncludingTaxes": "554.38",
      "totalDiscounts": "25.72",
      "totalFees": "387.6",
      "totalGlobalDiscounts": "5.7",
      "totalLocalDiscounts": "20.02",
      "totalPrice": "100.1",
      "totalTaxes": "92.4",
      "totalTaxesExcludingGlobalDiscount": "93.54",
      "unitCost": "46.2",
      "unitFee": "38.76",
      "unitPrice": 10.01,
    },
  },
  Object {
    "TAG": 0,
    "_0": Object {
      "availablesFeeKinds": Array [
        0,
        2,
      ],
      "capacityUnit": undefined,
      "description": "Description of product 10",
      "discounts": Array [],
      "expectedQuantity": 3,
      "expectedQuantityWarning": Array [],
      "fees": Array [
        Object {
          "amount": 3.33,
          "formattedAmount": "3.33 €",
          "formattedTotalAmount": "9.99 €",
          "id": "v4-uuid-24",
          "kind": 1,
          "totalAmount": "9.99",
        },
      ],
      "formattedExpectedQuantity": "3",
      "formattedQuantity": "3",
      "formattedStock": "33",
      "formattedTotalAmountExcludingGlobalDiscounts": "78.99 €",
      "formattedTotalAmountExcludingTaxes": "78.03 €",
      "formattedTotalAmountIncludingTaxes": "79.67 €",
      "formattedTotalDiscounts": "0.96 €",
      "formattedTotalFees": "9.99 €",
      "formattedTotalLocalDiscounts": "0.00 €",
      "formattedTotalPrice": "69.00 €",
      "formattedUnitFee": "3.33 €",
      "formattedUnitPrice": "23.00 €",
      "id": "v4-uuid-25",
      "identifier": "v4-uuid-7",
      "name": "Product 10",
      "packaging": 3,
      "quantity": 3,
      "stock": 33,
      "stockKeepingUnit": "1479427200007",
      "taxes": Array [
        Object {
          "amount": "1.64",
          "formattedAmount": "1.64 €",
          "rate": 2.1,
        },
        Object {
          "amount": "0",
          "formattedAmount": "0.00 €",
          "rate": 20,
        },
      ],
      "totalAmountExcludingGlobalDiscounts": "78.99",
      "totalAmountExcludingTaxes": "78.03",
      "totalAmountIncludingTaxes": "79.67",
      "totalDiscounts": "0.96",
      "totalFees": "9.99",
      "totalGlobalDiscounts": "0.96",
      "totalLocalDiscounts": "0",
      "totalPrice": "69",
      "totalTaxes": "1.64",
      "totalTaxesExcludingGlobalDiscount": "1.66",
      "unitCost": "26.01",
      "unitFee": "3.33",
      "unitPrice": 23,
    },
  },
]
`;

exports[`reducer StandardTaxRateUpdated should match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.24 €",
  "formattedTotalAmountExcludingTaxes": "471.58 €",
  "formattedTotalAmountIncludingTaxes": "517.62 €",
  "formattedTotalAmountOfGoods": "73.99 €",
  "formattedTotalTaxes": "46.04 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.17 €",
        "formattedTotalAmountIncludingTaxes": "506.99 €",
        "formattedTotalDiscounts": "26.53 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "28.21",
            "formattedAmount": "28.21 €",
            "rate": 20,
          },
          Object {
            "amount": "17.61",
            "formattedAmount": "17.61 €",
            "rate": 5.5,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.17",
        "totalAmountIncludingTaxes": "506.99",
        "totalDiscounts": "26.53",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.51",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "45.82",
        "totalTaxesExcludingGlobalDiscount": "47.13",
        "unitCost": "46.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.41 €",
        "formattedTotalAmountIncludingTaxes": "10.63 €",
        "formattedTotalDiscounts": "0.15 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0.22",
            "formattedAmount": "0.22 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 5.5,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.41",
        "totalAmountIncludingTaxes": "10.63",
        "totalDiscounts": "0.15",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.15",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0.22",
        "totalTaxesExcludingGlobalDiscount": "0.22",
        "unitCost": "3.47",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 5.5,
  "taxes": Array [
    Object {
      "amount": "0.22",
      "formattedAmount": "0.22 €",
      "rate": 2.1,
    },
    Object {
      "amount": "17.61",
      "formattedAmount": "17.61 €",
      "rate": 5.5,
    },
    Object {
      "amount": "28.21",
      "formattedAmount": "28.21 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "478.24",
  "totalAmountExcludingTaxes": "471.58",
  "totalAmountIncludingTaxes": "517.62",
  "totalAmountOfGoods": "73.99",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": "46.04",
}
`;

exports[`reducer TaxesFreeToggleRequested should match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-26",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "10.89",
      "formattedAmount": "10.89 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "478.24 €",
  "formattedTotalAmountExcludingTaxes": "471.58 €",
  "formattedTotalAmountIncludingTaxes": "471.58 €",
  "formattedTotalAmountOfGoods": "73.99 €",
  "formattedTotalTaxes": undefined,
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-22",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-19",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-20",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-21",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.17 €",
        "formattedTotalAmountIncludingTaxes": "461.17 €",
        "formattedTotalDiscounts": "26.53 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-23",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.17",
        "totalAmountIncludingTaxes": "461.17",
        "totalDiscounts": "26.53",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.51",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "0",
        "totalTaxesExcludingGlobalDiscount": "0",
        "unitCost": "46.12",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 10",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 3.33,
            "formattedAmount": "3.33 €",
            "formattedTotalAmount": "9.99 €",
            "id": "v4-uuid-24",
            "kind": 1,
            "totalAmount": "9.99",
          },
        ],
        "formattedExpectedQuantity": "3",
        "formattedQuantity": "3",
        "formattedStock": "33",
        "formattedTotalAmountExcludingGlobalDiscounts": "10.56 €",
        "formattedTotalAmountExcludingTaxes": "10.41 €",
        "formattedTotalAmountIncludingTaxes": "10.41 €",
        "formattedTotalDiscounts": "0.15 €",
        "formattedTotalFees": "9.99 €",
        "formattedTotalLocalDiscounts": "0.00 €",
        "formattedTotalPrice": "0.57 €",
        "formattedUnitFee": "3.33 €",
        "formattedUnitPrice": "0.19 €",
        "id": "v4-uuid-25",
        "identifier": "v4-uuid-7",
        "name": "Product 10",
        "packaging": 3,
        "quantity": 3,
        "stock": 33,
        "stockKeepingUnit": "1479427200007",
        "taxes": Array [
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 2.1,
          },
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "10.56",
        "totalAmountExcludingTaxes": "10.41",
        "totalAmountIncludingTaxes": "10.41",
        "totalDiscounts": "0.15",
        "totalFees": "9.99",
        "totalGlobalDiscounts": "0.15",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.57",
        "totalTaxes": "0",
        "totalTaxesExcludingGlobalDiscount": "0",
        "unitCost": "3.47",
        "unitFee": "3.33",
        "unitPrice": 0.19,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": undefined,
  "taxesFree": true,
  "totalAmountExcludingGlobalDiscounts": "478.24",
  "totalAmountExcludingTaxes": "471.58",
  "totalAmountIncludingTaxes": "471.58",
  "totalAmountOfGoods": "73.99",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 13,
  "totalProductsQuantity": 13,
  "totalTaxes": undefined,
}
`;

exports[`reducer process should match snapshot 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-53",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "0.9",
      "formattedAmount": "0.90 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
  "formattedTotalAmountExcludingTaxes": "461.02 €",
  "formattedTotalAmountIncludingTaxes": "553.22 €",
  "formattedTotalAmountOfGoods": "73.42 €",
  "formattedTotalTaxes": "92.20 €",
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-51",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-48",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-49",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-50",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.02 €",
        "formattedTotalAmountIncludingTaxes": "553.22 €",
        "formattedTotalDiscounts": "26.68 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-52",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "92.2",
            "formattedAmount": "92.20 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.02",
        "totalAmountIncludingTaxes": "553.22",
        "totalDiscounts": "26.68",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.66",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "92.2",
        "totalTaxesExcludingGlobalDiscount": "93.54",
        "unitCost": "46.1",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "92.2",
      "formattedAmount": "92.20 €",
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "467.68",
  "totalAmountExcludingTaxes": "461.02",
  "totalAmountIncludingTaxes": "553.22",
  "totalAmountOfGoods": "73.42",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 10,
  "totalProductsQuantity": 10,
  "totalTaxes": "92.2",
}
`;

exports[`reducer process should match snapshot with taxesFree enabled 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "6.66",
      "formattedAmount": "6.66 €",
      "formattedValue": "6.66 €",
      "id": "v4-uuid-59",
      "kind": 0,
      "name": "Remise 6.66 currency",
      "quantity": "0",
      "value": 6.66,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "320.1",
      "formattedAmount": "320.10 €",
      "kind": 0,
    },
    Object {
      "amount": "0.9",
      "formattedAmount": "0.90 €",
      "kind": 1,
    },
    Object {
      "amount": "66.6",
      "formattedAmount": "66.60 €",
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
  "formattedTotalAmountExcludingTaxes": "461.02 €",
  "formattedTotalAmountIncludingTaxes": "461.02 €",
  "formattedTotalAmountOfGoods": "73.42 €",
  "formattedTotalTaxes": undefined,
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [
          Object {
            "amount": "20.02",
            "formattedAmount": "20.02 €",
            "formattedValue": "20 %",
            "id": "v4-uuid-57",
            "kind": 1,
            "name": "Remise 20%",
            "quantity": "0",
            "value": 20,
            "warnings": Array [],
          },
        ],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": "6.66 €",
            "formattedTotalAmount": "66.60 €",
            "id": "v4-uuid-54",
            "kind": 2,
            "totalAmount": "66.6",
          },
          Object {
            "amount": 0.09,
            "formattedAmount": "0.09 €",
            "formattedTotalAmount": "0.90 €",
            "id": "v4-uuid-55",
            "kind": 1,
            "totalAmount": "0.9",
          },
          Object {
            "amount": 32.01,
            "formattedAmount": "32.01 €",
            "formattedTotalAmount": "320.10 €",
            "id": "v4-uuid-56",
            "kind": 0,
            "totalAmount": "320.1",
          },
        ],
        "formattedExpectedQuantity": "10",
        "formattedQuantity": "10",
        "formattedStock": "3",
        "formattedTotalAmountExcludingGlobalDiscounts": "467.68 €",
        "formattedTotalAmountExcludingTaxes": "461.02 €",
        "formattedTotalAmountIncludingTaxes": "461.02 €",
        "formattedTotalDiscounts": "26.68 €",
        "formattedTotalFees": "387.60 €",
        "formattedTotalLocalDiscounts": "20.02 €",
        "formattedTotalPrice": "100.10 €",
        "formattedUnitFee": "38.76 €",
        "formattedUnitPrice": "10.01 €",
        "id": "v4-uuid-58",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "0",
            "formattedAmount": "0.00 €",
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "467.68",
        "totalAmountExcludingTaxes": "461.02",
        "totalAmountIncludingTaxes": "461.02",
        "totalDiscounts": "26.68",
        "totalFees": "387.6",
        "totalGlobalDiscounts": "6.66",
        "totalLocalDiscounts": "20.02",
        "totalPrice": "100.1",
        "totalTaxes": "0",
        "totalTaxesExcludingGlobalDiscount": "0",
        "unitCost": "46.1",
        "unitFee": "38.76",
        "unitPrice": 10.01,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": undefined,
  "taxesFree": true,
  "totalAmountExcludingGlobalDiscounts": "467.68",
  "totalAmountExcludingTaxes": "461.02",
  "totalAmountIncludingTaxes": "461.02",
  "totalAmountOfGoods": "73.42",
  "totalDiscounts": "26.68",
  "totalProductsExpectedQuantity": 10,
  "totalProductsQuantity": 10,
  "totalTaxes": undefined,
}
`;
