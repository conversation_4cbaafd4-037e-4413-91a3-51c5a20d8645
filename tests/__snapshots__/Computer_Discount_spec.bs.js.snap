// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ComputeDiscount Global compute should not be able to compute global free discount amount through compute 1`] = `"Free discount should not be applied globally!"`;

exports[`ComputeDiscount Global computeCurrency should match snapshot 1`] = `"0.01"`;

exports[`ComputeDiscount Global computeFree should not be able to apply global free discount 1`] = `"Free discount should not be applied globally!"`;

exports[`ComputeDiscount Global computePercent should match snapshot 1`] = `"7.09"`;

exports[`ComputeDiscount Local computeCurrency should match snapshot 1`] = `"0.01"`;

exports[`ComputeDiscount Local computePercent should match snapshot 1`] = `"8.71"`;
