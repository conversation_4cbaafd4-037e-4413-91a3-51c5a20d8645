// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tax Calculation Bug Fix Global discount + unit fees + different tax rates should match snapshot for cart with global discount and mixed tax rates 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [
    Object {
      "amount": "20.84",
      "formattedAmount": undefined,
      "formattedValue": undefined,
      "id": "v4-uuid-25",
      "kind": 1,
      "name": "Remise 20%",
      "quantity": "0",
      "value": 20,
      "warnings": Array [],
    },
  ],
  "fees": Array [
    Object {
      "amount": "10.9",
      "formattedAmount": undefined,
      "kind": 0,
    },
    Object {
      "amount": "0.27",
      "formattedAmount": undefined,
      "kind": 1,
    },
    Object {
      "amount": "6.66",
      "formattedAmount": undefined,
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": undefined,
  "formattedTotalAmountExcludingTaxes": undefined,
  "formattedTotalAmountIncludingTaxes": undefined,
  "formattedTotalAmountOfGoods": undefined,
  "formattedTotalTaxes": undefined,
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          1,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 1.09,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-19",
            "kind": 0,
            "totalAmount": "10.9",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-20",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "18.41",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "111",
        "totalAmountExcludingTaxes": "92.04",
        "totalAmountIncludingTaxes": "110.45",
        "totalDiscounts": "18.96",
        "totalFees": "10.9",
        "totalGlobalDiscounts": "18.96",
        "totalLocalDiscounts": "0",
        "totalPrice": "100.1",
        "totalTaxes": "18.41",
        "totalTaxesExcludingGlobalDiscount": "22.2",
        "unitCost": "9.2",
        "unitFee": "1.09",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 1",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 0.09,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-21",
            "kind": 1,
            "totalAmount": "0.27",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-22",
        "identifier": "v4-uuid-1",
        "name": "Product 1",
        "packaging": 1,
        "quantity": 3,
        "stock": 10,
        "stockKeepingUnit": "1479427200001",
        "taxes": Array [
          Object {
            "amount": "0.71",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "4.26",
        "totalAmountExcludingTaxes": "3.53",
        "totalAmountIncludingTaxes": "4.24",
        "totalDiscounts": "0.73",
        "totalFees": "0.27",
        "totalGlobalDiscounts": "0.73",
        "totalLocalDiscounts": "0",
        "totalPrice": "3.99",
        "totalTaxes": "0.71",
        "totalTaxesExcludingGlobalDiscount": "0.85",
        "unitCost": "1.18",
        "unitFee": "0.09",
        "unitPrice": 1.33,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 2",
        "discounts": Array [],
        "expectedQuantity": 1,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-23",
            "kind": 2,
            "totalAmount": "6.66",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-24",
        "identifier": "v4-uuid-2",
        "name": "Product 2",
        "packaging": 1,
        "quantity": 1,
        "stock": 10,
        "stockKeepingUnit": "1479427200002",
        "taxes": Array [
          Object {
            "amount": "0.31",
            "formattedAmount": undefined,
            "rate": 5.5,
          },
          Object {
            "amount": "0",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "6.75",
        "totalAmountExcludingTaxes": "5.6",
        "totalAmountIncludingTaxes": "5.91",
        "totalDiscounts": "1.15",
        "totalFees": "6.66",
        "totalGlobalDiscounts": "1.15",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.09",
        "totalTaxes": "0.31",
        "totalTaxesExcludingGlobalDiscount": "0.37",
        "unitCost": "5.6",
        "unitFee": "6.66",
        "unitPrice": 0.09,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.31",
      "formattedAmount": undefined,
      "rate": 5.5,
    },
    Object {
      "amount": "19.12",
      "formattedAmount": undefined,
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "122.01",
  "totalAmountExcludingTaxes": "101.17",
  "totalAmountIncludingTaxes": "120.6",
  "totalAmountOfGoods": "83.34",
  "totalDiscounts": "20.84",
  "totalProductsExpectedQuantity": 14,
  "totalProductsQuantity": 14,
  "totalTaxes": "19.43",
}
`;

exports[`Tax Calculation Bug Fix Global discount + unit fees + different tax rates should match snapshot for cart without global discount and mixed tax rates 1`] = `
Object {
  "currency": 0,
  "decimalPrecision": 2,
  "discounts": Array [],
  "fees": Array [
    Object {
      "amount": "10.9",
      "formattedAmount": undefined,
      "kind": 0,
    },
    Object {
      "amount": "0.27",
      "formattedAmount": undefined,
      "kind": 1,
    },
    Object {
      "amount": "6.66",
      "formattedAmount": undefined,
      "kind": 2,
    },
  ],
  "formattedTotalAmountExcludingGlobalDiscounts": undefined,
  "formattedTotalAmountExcludingTaxes": undefined,
  "formattedTotalAmountIncludingTaxes": undefined,
  "formattedTotalAmountOfGoods": undefined,
  "formattedTotalTaxes": undefined,
  "products": Array [
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          1,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 3",
        "discounts": Array [],
        "expectedQuantity": 10,
        "expectedQuantityWarning": Array [
          0,
        ],
        "fees": Array [
          Object {
            "amount": 1.09,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-26",
            "kind": 0,
            "totalAmount": "10.9",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-27",
        "identifier": "v4-uuid-3",
        "name": "Product 3",
        "packaging": 7,
        "quantity": 10,
        "stock": 3,
        "stockKeepingUnit": "1479427200003",
        "taxes": Array [
          Object {
            "amount": "22.2",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "111",
        "totalAmountExcludingTaxes": "111",
        "totalAmountIncludingTaxes": "133.2",
        "totalDiscounts": "0",
        "totalFees": "10.9",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "100.1",
        "totalTaxes": "22.2",
        "totalTaxesExcludingGlobalDiscount": "22.2",
        "unitCost": "11.1",
        "unitFee": "1.09",
        "unitPrice": 10.01,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          2,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 1",
        "discounts": Array [],
        "expectedQuantity": 3,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 0.09,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-28",
            "kind": 1,
            "totalAmount": "0.27",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-29",
        "identifier": "v4-uuid-1",
        "name": "Product 1",
        "packaging": 1,
        "quantity": 3,
        "stock": 10,
        "stockKeepingUnit": "1479427200001",
        "taxes": Array [
          Object {
            "amount": "0.85",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "4.26",
        "totalAmountExcludingTaxes": "4.26",
        "totalAmountIncludingTaxes": "5.11",
        "totalDiscounts": "0",
        "totalFees": "0.27",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "3.99",
        "totalTaxes": "0.85",
        "totalTaxesExcludingGlobalDiscount": "0.85",
        "unitCost": "1.42",
        "unitFee": "0.09",
        "unitPrice": 1.33,
      },
    },
    Object {
      "TAG": 0,
      "_0": Object {
        "availablesFeeKinds": Array [
          0,
          1,
        ],
        "capacityUnit": undefined,
        "description": "Description of product 2",
        "discounts": Array [],
        "expectedQuantity": 1,
        "expectedQuantityWarning": Array [],
        "fees": Array [
          Object {
            "amount": 6.66,
            "formattedAmount": undefined,
            "formattedTotalAmount": undefined,
            "id": "v4-uuid-30",
            "kind": 2,
            "totalAmount": "6.66",
          },
        ],
        "formattedExpectedQuantity": undefined,
        "formattedQuantity": undefined,
        "formattedStock": undefined,
        "formattedTotalAmountExcludingGlobalDiscounts": undefined,
        "formattedTotalAmountExcludingTaxes": undefined,
        "formattedTotalAmountIncludingTaxes": undefined,
        "formattedTotalDiscounts": undefined,
        "formattedTotalFees": undefined,
        "formattedTotalLocalDiscounts": undefined,
        "formattedTotalPrice": undefined,
        "formattedUnitFee": undefined,
        "formattedUnitPrice": undefined,
        "id": "v4-uuid-31",
        "identifier": "v4-uuid-2",
        "name": "Product 2",
        "packaging": 1,
        "quantity": 1,
        "stock": 10,
        "stockKeepingUnit": "1479427200002",
        "taxes": Array [
          Object {
            "amount": "0.37",
            "formattedAmount": undefined,
            "rate": 5.5,
          },
          Object {
            "amount": "0",
            "formattedAmount": undefined,
            "rate": 20,
          },
        ],
        "totalAmountExcludingGlobalDiscounts": "6.75",
        "totalAmountExcludingTaxes": "6.75",
        "totalAmountIncludingTaxes": "7.12",
        "totalDiscounts": "0",
        "totalFees": "6.66",
        "totalGlobalDiscounts": "0",
        "totalLocalDiscounts": "0",
        "totalPrice": "0.09",
        "totalTaxes": "0.37",
        "totalTaxesExcludingGlobalDiscount": "0.37",
        "unitCost": "6.75",
        "unitFee": "6.66",
        "unitPrice": 0.09,
      },
    },
  ],
  "standardTaxRate": 20,
  "taxes": Array [
    Object {
      "amount": "0.37",
      "formattedAmount": undefined,
      "rate": 5.5,
    },
    Object {
      "amount": "23.05",
      "formattedAmount": undefined,
      "rate": 20,
    },
  ],
  "taxesFree": false,
  "totalAmountExcludingGlobalDiscounts": "122.01",
  "totalAmountExcludingTaxes": "122.01",
  "totalAmountIncludingTaxes": "145.43",
  "totalAmountOfGoods": "104.18",
  "totalDiscounts": "0",
  "totalProductsExpectedQuantity": 14,
  "totalProductsQuantity": 14,
  "totalTaxes": "23.42",
}
`;
