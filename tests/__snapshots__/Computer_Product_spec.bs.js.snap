// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ComputeProduct computeExpectedQuantityWarning should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeGlobalDiscounts should math snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeProduct bulk products should match snapshot 1`] = `
Object {
  "TAG": 1,
  "_0": Object {
    "availablesFeeKinds": Array [
      0,
      1,
      2,
    ],
    "capacityUnit": "kg",
    "description": "Description of product 14",
    "discounts": Array [],
    "expectedQuantity": "420.9",
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-36",
    "identifier": "v4-uuid-9",
    "name": "Product 14",
    "packaging": "1",
    "quantity": "420.9",
    "stock": "13",
    "stockKeepingUnit": "1479427200009",
    "taxes": Array [
      Object {
        "amount": "276.96",
        "formattedAmount": undefined,
        "rate": 5.5,
      },
      Object {
        "amount": "0",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "5046.59",
    "totalAmountExcludingTaxes": "5035.61",
    "totalAmountIncludingTaxes": "5312.57",
    "totalDiscounts": "10.98",
    "totalFees": "0",
    "totalGlobalDiscounts": "10.98",
    "totalLocalDiscounts": "0",
    "totalPrice": "5046.59",
    "totalTaxes": "276.96",
    "totalTaxesExcludingGlobalDiscount": "277.56",
    "unitCost": "11.96",
    "unitFee": "0",
    "unitPrice": 11.99,
  },
  "_1": 3,
}
`;

exports[`ComputeProduct computeTaxesAmounts should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalAmountExcludingGlobalDiscounts should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalAmountWithTaxes should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalAmounts should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalFees should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalLocalDiscounts should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalPrice should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalTaxes should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeTotalTaxesExcludingGlobalDiscount should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeUnitCost should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct computeUnitFee should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct postCompute should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;

exports[`ComputeProduct preCompute should match snapshot 1`] = `
Object {
  "TAG": 0,
  "_0": Object {
    "availablesFeeKinds": Array [
      1,
      2,
    ],
    "capacityUnit": undefined,
    "description": "Description of product 3",
    "discounts": Array [
      Object {
        "amount": "0.33",
        "formattedAmount": undefined,
        "formattedValue": undefined,
        "id": "v4-uuid-34",
        "kind": 0,
        "name": "Remise 0.33 currency",
        "quantity": "0",
        "value": 0.33,
        "warnings": Array [],
      },
    ],
    "expectedQuantity": 10,
    "expectedQuantityWarning": Array [
      0,
    ],
    "fees": Array [
      Object {
        "amount": 6.66,
        "formattedAmount": undefined,
        "formattedTotalAmount": undefined,
        "id": "v4-uuid-33",
        "kind": 0,
        "totalAmount": "66.6",
      },
    ],
    "formattedExpectedQuantity": undefined,
    "formattedQuantity": undefined,
    "formattedStock": undefined,
    "formattedTotalAmountExcludingGlobalDiscounts": undefined,
    "formattedTotalAmountExcludingTaxes": undefined,
    "formattedTotalAmountIncludingTaxes": undefined,
    "formattedTotalDiscounts": undefined,
    "formattedTotalFees": undefined,
    "formattedTotalLocalDiscounts": undefined,
    "formattedTotalPrice": undefined,
    "formattedUnitFee": undefined,
    "formattedUnitPrice": undefined,
    "id": "v4-uuid-35",
    "identifier": "v4-uuid-3",
    "name": "Product 3",
    "packaging": 7,
    "quantity": 10,
    "stock": 3,
    "stockKeepingUnit": "1479427200003",
    "taxes": Array [
      Object {
        "amount": "32.61",
        "formattedAmount": undefined,
        "rate": 20,
      },
    ],
    "totalAmountExcludingGlobalDiscounts": "166.37",
    "totalAmountExcludingTaxes": "163.05",
    "totalAmountIncludingTaxes": "195.66",
    "totalDiscounts": "3.65",
    "totalFees": "66.6",
    "totalGlobalDiscounts": "3.32",
    "totalLocalDiscounts": "0.33",
    "totalPrice": "100.1",
    "totalTaxes": "32.61",
    "totalTaxesExcludingGlobalDiscount": "33.27",
    "unitCost": "16.31",
    "unitFee": "6.66",
    "unitPrice": 10.01,
  },
}
`;
