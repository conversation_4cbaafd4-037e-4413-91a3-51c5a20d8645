open Jest
open Expect
open Accounting__Types

module Maker = Accounting__Maker
module Computer = Accounting__Computer

// Test for the specific bug: Global discount + unit fees + different tax rates
// This test verifies that unit fees only affect their assigned tax rate
// and don't incorrectly modify other tax rates when global discounts are present

describe("Tax Calculation Bug Fix", () => {
  describe("Global discount + unit fees + different tax rates", () => {
    // Create a cart with global discount and products with different tax rates and unit fees
    let productA = Fixtures__ProductInputs.make(
      Fixtures__ProductInputs.quantity_10_unit_price_10_01_tax_20,
      ~fees=[Fixtures__FeeInputs.transport_1_09], // Unit fee with transport
      (),
    )

    let productB = Fixtures__ProductInputs.make(
      Fixtures__ProductInputs.quantity_3_unit_price_1_33_tax_20,
      ~fees=[Fixtures__FeeInputs.tax_0_09], // Unit fee with tax
      (),
    )

    let productC = Fixtures__ProductInputs.make(
      Fixtures__ProductInputs.quantity_1_unit_price_0_09_tax_5_5, // Different tax rate (5.5% vs 20%)
      ~fees=[Fixtures__FeeInputs.other_6_66], // Unit fee with other
      (),
    )

    let cartWithGlobalDiscount =
      {
        products: [productA, productB, productC],
        discounts: [Fixtures__DiscountInputs.percent_20], // 20% global discount
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    let cartWithoutGlobalDiscount =
      {
        products: [productA, productB, productC],
        discounts: [], // No global discount
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test(
      "should isolate unit fee tax effects to their specific tax rates",
      () => {
        // Get taxes from both carts
        let taxesWithGlobalDiscount = cartWithGlobalDiscount.taxes->Option.getExn
        let taxesWithoutGlobalDiscount = cartWithoutGlobalDiscount.taxes->Option.getExn

        // Both carts should have the same tax rate structure
        expect(taxesWithGlobalDiscount->Array.length) |> toBe(
          taxesWithoutGlobalDiscount->Array.length,
        )
      },
    )

    test(
      "should apply global discounts proportionally to all tax rates",
      () => {
        let taxesWithGlobalDiscount = cartWithGlobalDiscount.taxes->Option.getExn
        let taxesWithoutGlobalDiscount = cartWithoutGlobalDiscount.taxes->Option.getExn

        // Find taxes by rate
        let tax20WithGlobal =
          taxesWithGlobalDiscount->Array.getBy(tax => tax.rate === 20.)->Option.getExn
        let tax5_5WithGlobal =
          taxesWithGlobalDiscount->Array.getBy(tax => tax.rate === 5.5)->Option.getExn
        let tax20WithoutGlobal =
          taxesWithoutGlobalDiscount->Array.getBy(tax => tax.rate === 20.)->Option.getExn
        let tax5_5WithoutGlobal =
          taxesWithoutGlobalDiscount->Array.getBy(tax => tax.rate === 5.5)->Option.getExn

        // Tax amounts with global discount should be less than without global discount
        // (because global discount reduces the taxable base)
        let _ =
          expect(tax20WithGlobal.amount->Option.getExn->Big.toFloat) |> toBeLessThan(
            tax20WithoutGlobal.amount->Option.getExn->Big.toFloat,
          )
        expect(tax5_5WithGlobal.amount->Option.getExn->Big.toFloat) |> toBeLessThan(
          tax5_5WithoutGlobal.amount->Option.getExn->Big.toFloat,
        )
      },
    )

    test(
      "should maintain correct tax calculation ratios between different rates",
      () => {
        let taxesWithGlobalDiscount = cartWithGlobalDiscount.taxes->Option.getExn
        let taxesWithoutGlobalDiscount = cartWithoutGlobalDiscount.taxes->Option.getExn

        // Find taxes by rate
        let tax20WithGlobal =
          taxesWithGlobalDiscount->Array.getBy(tax => tax.rate === 20.)->Option.getExn
        let tax5_5WithGlobal =
          taxesWithGlobalDiscount->Array.getBy(tax => tax.rate === 5.5)->Option.getExn
        let tax20WithoutGlobal =
          taxesWithoutGlobalDiscount->Array.getBy(tax => tax.rate === 20.)->Option.getExn
        let tax5_5WithoutGlobal =
          taxesWithoutGlobalDiscount->Array.getBy(tax => tax.rate === 5.5)->Option.getExn

        // Calculate the ratio of reduction due to global discount for each tax rate
        let reduction20 =
          (tax20WithoutGlobal.amount->Option.getExn->Big.toFloat -.
            tax20WithGlobal.amount->Option.getExn->Big.toFloat) /.
            tax20WithoutGlobal.amount->Option.getExn->Big.toFloat
        let reduction5_5 =
          (tax5_5WithoutGlobal.amount->Option.getExn->Big.toFloat -.
            tax5_5WithGlobal.amount->Option.getExn->Big.toFloat) /.
            tax5_5WithoutGlobal.amount->Option.getExn->Big.toFloat

        // The reduction ratios should be similar (within a reasonable tolerance)
        // This ensures that global discounts affect all tax rates proportionally
        let tolerance = 0.05 // 5% tolerance
        expect(Js.Math.abs_float(reduction20 -. reduction5_5)) |> toBeLessThan(tolerance)
      },
    )

    test(
      "should match snapshot for cart with global discount and mixed tax rates",
      () => {
        expect(cartWithGlobalDiscount) |> toMatchSnapshot
      },
    )

    test(
      "should match snapshot for cart without global discount and mixed tax rates",
      () => {
        expect(cartWithoutGlobalDiscount) |> toMatchSnapshot
      },
    )
  })
})
