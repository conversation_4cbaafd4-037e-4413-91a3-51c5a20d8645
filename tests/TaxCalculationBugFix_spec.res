open Jest
open Expect
open Accounting__Types

module Maker = Accounting__Maker
module Computer = Accounting__Computer

// Test for the specific bug: Global discount + unit fees + different tax rates
// This test verifies that unit fees only affect their assigned tax rate
// and don't incorrectly modify other tax rates when global discounts are present

describe("Tax Calculation Bug Fix", () => {
  describe("Global discount + unit fees + different tax rates", () => {
    // Create a cart with global discount and products with different tax rates and unit fees
    let productA = Fixtures__ProductInputs.make(
      Fixtures__ProductInputs.quantity_10_unit_price_10_01_tax_20,
      ~fees=[Fixtures__FeeInputs.transport_1_09], // Unit fee with transport
      (),
    )

    let productB = Fixtures__ProductInputs.make(
      Fixtures__ProductInputs.quantity_3_unit_price_1_33_tax_20,
      ~fees=[Fixtures__FeeInputs.tax_0_09], // Unit fee with tax
      (),
    )

    let productC = Fixtures__ProductInputs.make(
      Fixtures__ProductInputs.quantity_1_unit_price_0_09_tax_5_5, // Different tax rate (5.5% vs 20%)
      ~fees=[Fixtures__FeeInputs.other_6_66], // Unit fee with other
      (),
    )

    let cartWithGlobalDiscount =
      {
        products: [productA, productB, productC],
        discounts: [Fixtures__DiscountInputs.percent_20], // 20% global discount
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    let cartWithoutGlobalDiscount =
      {
        products: [productA, productB, productC],
        discounts: [], // No global discount
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make

    test(
      "should isolate unit fee tax effects to their specific tax rates",
      () => {
        // Get taxes from both carts
        let taxesWithGlobalDiscount = cartWithGlobalDiscount.taxes->Option.getExn
        let taxesWithoutGlobalDiscount = cartWithoutGlobalDiscount.taxes->Option.getExn

        // Both carts should have the same tax rate structure
        expect(taxesWithGlobalDiscount->Array.length) |> toBe(
          taxesWithoutGlobalDiscount->Array.length,
        )
      },
    )

    test(
      "should distribute global discounts based on merchandise amounts only",
      () => {
        // Test that global discount distribution is based on merchandise amounts, not total amounts including fees

        // Get products from cart with global discount
        let productA = cartWithGlobalDiscount.products->Array.getExn(0) // High fees
        let productB = cartWithGlobalDiscount.products->Array.getExn(1) // Medium fees
        let productC = cartWithGlobalDiscount.products->Array.getExn(2) // High fees, different tax rate

        // Extract merchandise amounts (price - local discounts) and global discounts
        let (merchandiseA, globalDiscountA) = switch productA {
        | Unit({totalPrice, totalLocalDiscounts, totalGlobalDiscounts}) => (
            totalPrice->Option.getExn->Big.toFloat -.
              totalLocalDiscounts->Option.getExn->Big.toFloat,
            totalGlobalDiscounts->Option.getExn->Big.toFloat,
          )
        | _ => (0., 0.)
        }

        let (merchandiseB, globalDiscountB) = switch productB {
        | Unit({totalPrice, totalLocalDiscounts, totalGlobalDiscounts}) => (
            totalPrice->Option.getExn->Big.toFloat -.
              totalLocalDiscounts->Option.getExn->Big.toFloat,
            totalGlobalDiscounts->Option.getExn->Big.toFloat,
          )
        | _ => (0., 0.)
        }

        let (merchandiseC, globalDiscountC) = switch productC {
        | Unit({totalPrice, totalLocalDiscounts, totalGlobalDiscounts}) => (
            totalPrice->Option.getExn->Big.toFloat -.
              totalLocalDiscounts->Option.getExn->Big.toFloat,
            totalGlobalDiscounts->Option.getExn->Big.toFloat,
          )
        | _ => (0., 0.)
        }

        // Calculate ratios: global discount should be proportional to merchandise amount
        let totalMerchandise = merchandiseA +. merchandiseB +. merchandiseC
        let expectedRatioA = merchandiseA /. totalMerchandise
        let expectedRatioB = merchandiseB /. totalMerchandise
        let expectedRatioC = merchandiseC /. totalMerchandise

        let totalGlobalDiscount = globalDiscountA +. globalDiscountB +. globalDiscountC
        let actualRatioA = globalDiscountA /. totalGlobalDiscount
        let actualRatioB = globalDiscountB /. totalGlobalDiscount
        let actualRatioC = globalDiscountC /. totalGlobalDiscount

        // Ratios should match within a small tolerance (accounting for rounding)
        let tolerance = 0.01
        let _ = expect(Js.Math.abs_float(actualRatioA -. expectedRatioA)) |> toBeLessThan(tolerance)
        let _ = expect(Js.Math.abs_float(actualRatioB -. expectedRatioB)) |> toBeLessThan(tolerance)
        expect(Js.Math.abs_float(actualRatioC -. expectedRatioC)) |> toBeLessThan(tolerance)
      },
    )

    test(
      "should match snapshot for cart with global discount and mixed tax rates",
      () => {
        expect(cartWithGlobalDiscount) |> toMatchSnapshot
      },
    )

    test(
      "should match snapshot for cart without global discount and mixed tax rates",
      () => {
        expect(cartWithoutGlobalDiscount) |> toMatchSnapshot
      },
    )
  })
})
