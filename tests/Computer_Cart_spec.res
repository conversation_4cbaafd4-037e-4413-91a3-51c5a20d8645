open Jest
open Fixtures
open Accounting__Types

open Expect
open ProductInputs

module Maker = Accounting__Maker
module ComputerCart = Accounting__Computer__Cart
module Computer = Accounting__Computer

describe("computeTotalProductsQuantity", () => {
  test("should return total quantities from all products", () => {
    let cart = {
      products: [quantity_3_unit_price_0_19_tax_2_1, quantity_3_unit_price_1_33_tax_20],
      discounts: [],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }->Maker.Cart.make
    let input = cart->ComputerCart.computeTotalProductsQuantity

    expect((input.totalProductsQuantity, input.totalProductsExpectedQuantity)) |> toEqual((
      Some(6),
      Some(6),
    ))
  })

  test("should return total quantities counting bulk product as plus one", () => {
    let cart = {
      products: [
        quantity_3_unit_price_0_19_tax_2_1,
        bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20,
      ],
      discounts: [],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }->Maker.Cart.make
    let input = cart->ComputerCart.computeTotalProductsQuantity

    expect((input.totalProductsQuantity, input.totalProductsExpectedQuantity)) |> toEqual((
      Some(4),
      Some(4),
    ))
  })
})

describe("compute", () => {
  describe("with 2 decimal precision", () => {
    let quantity_10_unit_price_10_01_tax_20 = {
      make(quantity_10_unit_price_10_01_tax_20, ~discounts=[], ~fees=[FeeInputs.transport_6_66], ())
    }
    let quantity_3_unit_price_1_33_tax_20 = {
      make(
        quantity_3_unit_price_1_33_tax_20,
        ~discounts=[DiscountInputs.currency_0_33],
        ~fees=[FeeInputs.transport_6_66],
        (),
      )
    }
    let quantity_1_unit_price_0_09_tax_5_5 = {
      make(
        quantity_1_unit_price_0_09_tax_5_5,
        ~discounts=[DiscountInputs.currency_12_09],
        ~fees=[],
        (),
      )
    }
    let quantity_13_unit_price_20_11_tax_10 = {
      make(
        quantity_13_unit_price_20_11_tax_10,
        ~discounts=[DiscountInputs.percent_20],
        ~fees=[FeeInputs.transport_1_09, FeeInputs.tax_0_09],
        (),
      )
    }
    let quantity_0_unit_price_20_1281_tax_20 = {
      make(
        quantity_0_unit_price_20_1281_tax_20,
        ~discounts=[DiscountInputs.free_3],
        ~fees=[FeeInputs.transport_1_09, FeeInputs.other_0_01],
        (),
      )
    }
    let quantity_3_unit_price_0_19_tax_2_1 = {
      make(
        quantity_3_unit_price_0_19_tax_2_1,
        ~discounts=[DiscountInputs.free_0],
        ~fees=[FeeInputs.transport_6_66, FeeInputs.tax_3_33, FeeInputs.other_6_66],
        (),
      )
    }

    describe(
      "cart excluding taxes with usd currency and a global discount",
      () => {
        let initialCart = {
          products: [
            quantity_10_unit_price_10_01_tax_20,
            quantity_3_unit_price_0_19_tax_2_1,
            quantity_3_unit_price_1_33_tax_20,
            quantity_1_unit_price_0_09_tax_5_5,
            quantity_0_unit_price_20_1281_tax_20,
            quantity_13_unit_price_20_11_tax_10,
          ],
          discounts: [DiscountInputs.percent_3_33],
          decimalPrecision: 2,
          currency: Usd,
          taxesFree: false,
          standardTaxRate: 20.,
        }->Maker.Cart.make

        test("should match snapshot", () => expect(initialCart->Computer.make) |> toMatchSnapshot)
      },
    )

    describe(
      "cart with taxes and taxesFree enabled",
      () => {
        let initialCart = {
          products: [
            quantity_3_unit_price_0_19_tax_2_1,
            quantity_3_unit_price_1_33_tax_20,
            quantity_1_unit_price_0_09_tax_5_5,
          ],
          discounts: [],
          decimalPrecision: 2,
          currency: Eur,
          taxesFree: true,
          standardTaxRate: 20.,
        }->Maker.Cart.make

        describe(
          "compute",
          () => {
            let cart = initialCart->Computer.make

            test(
              "cart taxes should not be computed",
              () => expect((cart.taxes, cart.totalTaxes)) |> toEqual((None, None)),
            )
            test(
              "products taxes amounts should be computed with values of 0",
              () => {
                let productTaxes = cart.products->Array.map(
                  product =>
                    switch product {
                    | Unit({taxes}) | Bulk({taxes}, _) => taxes
                    },
                )

                expect(productTaxes) |> toEqual([
                  Some([
                    {
                      rate: 2.1,
                      amount: Some(0.->Big.fromFloat),
                      formattedAmount: None,
                    },
                  ]),
                  Some([
                    {
                      rate: 20.,
                      amount: Some(0.->Big.fromFloat),
                      formattedAmount: None,
                    },
                  ]),
                  Some([
                    {
                      rate: 5.5,
                      amount: Some(0.->Big.fromFloat),
                      formattedAmount: None,
                    },
                  ]),
                ])
              },
            )
            test(
              "product amounts including taxes should be equal to amounts excluding taxes",
              () => {
                expect(
                  switch cart.products->Array.getExn(0) {
                  | Unit({totalAmountExcludingTaxes, totalAmountIncludingTaxes})
                  | Bulk({totalAmountExcludingTaxes, totalAmountIncludingTaxes}, _) => (
                      totalAmountExcludingTaxes,
                      totalAmountIncludingTaxes,
                    )
                  },
                ) |> toEqual((Some(50.52->Big.fromFloat), Some(50.52->Big.fromFloat)))
              },
            )
            test("should match snapshot", () => expect(cart)->toMatchSnapshot)
          },
        )
      },
    )

    describe(
      "cart with transport fee and different standardTaxRate",
      () => {
        describe(
          "compute",
          () => {
            let initialCart = {
              products: [quantity_10_unit_price_10_01_tax_20],
              discounts: [],
              decimalPrecision: 2,
              currency: Eur,
              taxesFree: false,
              standardTaxRate: 10.,
            }->Maker.Cart.make
            let cart = initialCart->Computer.make

            test(
              "products taxes amounts based off Transport fee should be computed with the standardTaxRate",
              () => {
                let productTaxes = cart.products->Array.map(
                  product =>
                    switch product {
                    | Unit({taxes}) | Bulk({taxes}, _) => taxes
                    },
                )

                expect(productTaxes) |> toEqual([
                  Some([
                    {
                      rate: 20.,
                      amount: Some(20.02->Big.fromFloat),
                      formattedAmount: None,
                    },
                    {
                      rate: 10.,
                      amount: Some(6.66->Big.fromFloat),
                      formattedAmount: None,
                    },
                  ]),
                ])
              },
            )

            let initialCart = {
              products: [quantity_10_unit_price_10_01_tax_20],
              discounts: [],
              decimalPrecision: 2,
              currency: Eur,
              taxesFree: false,
              standardTaxRate: 20.,
            }->Maker.Cart.make

            let cart = initialCart->Computer.make

            test(
              "products taxes should be unique",
              () => {
                let productTaxes = cart.products->Array.map(
                  product =>
                    switch product {
                    | Unit({taxes}) | Bulk({taxes}, _) => taxes
                    },
                )

                expect(productTaxes) |> toEqual([
                  Some([
                    {
                      rate: 20.,
                      amount: Some(33.34->Big.fromFloat),
                      formattedAmount: None,
                    },
                  ]),
                ])
              },
            )

            test("should match snapshot", () => expect(cart)->toMatchSnapshot)
          },
        )
      },
    )
  })

  describe("with 3 decimal precision", () =>
    describe(
      "cart excluding taxes with eur currency and a global discount",
      () => {
        let quantity_10_unit_price_10_01_tax_20 = {
          make(quantity_10_unit_price_10_01_tax_20, ~discounts=[], ~fees=[FeeInputs.tax_0_001], ())
        }
        let quantity_13_unit_price_20_11_tax_10 = {
          make(
            quantity_13_unit_price_20_11_tax_10,
            ~discounts=[DiscountInputs.percent_20],
            ~fees=[FeeInputs.transport_1_999, FeeInputs.tax_0_001],
            (),
          )
        }
        let quantity_3_unit_price_0_19_tax_2_1 = {
          make(
            quantity_3_unit_price_0_19_tax_2_1,
            ~discounts=[DiscountInputs.free_0],
            ~fees=[FeeInputs.transport_1_999, FeeInputs.tax_0_001, FeeInputs.other_1_901],
            (),
          )
        }
        let quantity_301_unit_price_19_881_tax_20 = {
          make(
            quantity_301_unit_price_19_881_tax_20,
            ~discounts=[],
            ~fees=[FeeInputs.transport_1_999],
            (),
          )
        }
        let bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20 = {
          make(
            bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20,
            ~discounts=[DiscountInputs.percent_3_33],
            ~fees=[FeeInputs.tax_3_33, FeeInputs.other_1_901],
            (),
          )
        }

        let initialCart = {
          products: [
            quantity_10_unit_price_10_01_tax_20,
            quantity_3_unit_price_0_19_tax_2_1,
            quantity_13_unit_price_20_11_tax_10,
            quantity_301_unit_price_19_881_tax_20,
            bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20,
          ],
          discounts: [DiscountInputs.currency_0_001],
          decimalPrecision: 3,
          currency: Eur,
          taxesFree: false,
          standardTaxRate: 20.,
        }->Maker.Cart.make

        test("should match snapshot", () => expect(initialCart->Computer.make) |> toMatchSnapshot)
      },
    )
  )
})
