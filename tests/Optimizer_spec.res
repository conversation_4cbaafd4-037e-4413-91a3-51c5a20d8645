open Jest
open Fixtures
open Accounting__Reducer
open Accounting__Actions

module Utils = Accounting__Utils
module Maker = Accounting__Maker
module Computer = Accounting__Computer
module Formatter = Accounting__Formatter

describe("Optimizer", () => {
  open Expect

  let productInputA = {
    open ProductInputs
    make(
      quantity_10_unit_price_10_01_tax_20,
      ~id=Some("test-id"),
      ~discounts=[DiscountInputs.percent_20],
      ~fees=[FeeInputs.other_6_66, FeeInputs.tax_0_09, FeeInputs.transport_32_01],
      (),
    )
  }

  let productInputAId = switch productInputA {
  | Unit({product: {id}}) | Bulk({product: {id}, _}) => id
  }

  let productInputB = {
    open ProductInputs
    make(
      quantity_13_unit_price_20_11_tax_10,
      ~discounts=[DiscountInputs.percent_3_33],
      ~fees=[FeeInputs.transport_32_01],
      (),
    )
  }
  let productInputC = {
    open ProductInputs
    make(quantity_3_unit_price_1_33_tax_20, ~fees=[FeeInputs.tax_3_33], ())
  }

  let cart__0 =
    {
      products: [productInputA, productInputB],
      discounts: [],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }
    ->Maker.Cart.make
    ->Computer.make
    ->Formatter.make

  let cart__1 = cart__0->reducer(ProductAdded(productInputC))

  let cart__2 = cart__1->reducer(GlobalDiscountAdded(DiscountInputs.percent_3_33))

  test("should shallow equal", () =>
    expect(cart__0.products->Utils.getProductByKey(productInputAId)) |> toEqual(
      cart__1.products->Utils.getProductByKey(productInputAId),
    )
  )

  test("should not shallow equal", () =>
    expect(cart__1.products->Utils.getProductByKey(productInputAId))
    |> not_
    |> toEqual(cart__2.products->Utils.getProductByKey(productInputAId))
  )
})
