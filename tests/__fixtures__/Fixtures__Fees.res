open Accounting__Types
open Accounting__Utils

// Transport fees
@genType.as("fees__transport_32_01")
let transport_32_01: fee = {
  kind: Transport,
  amount: 32.01,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__transport_6_66")
let transport_6_66 = {
  kind: Transport,
  amount: 6.66,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__transport_0_93")
let transport_0_93 = {
  kind: Transport,
  amount: 0.93,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__transport_1_09")
let transport_1_09 = {
  kind: Transport,
  amount: 1.09,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__transport_error_negative_0_01")
let transport_error_negative_0_01 = {
  kind: Transport,
  amount: -0.01,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

// Taxes fees
@genType.as("fees__tax_11_9")
let tax_11_9 = {
  kind: Taxes,
  amount: 11.9,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__tax_0_09")
let tax_0_09 = {
  kind: Taxes,
  amount: 0.09,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__tax_3_33")
let tax_3_33 = {
  kind: Taxes,
  amount: 3.33,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__tax_error_negative_0_09")
let tax_error_negative_0_09 = {
  kind: Taxes,
  amount: -0.09,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

// Others fees
@genType.as("fees__other_2_03")
let other_2_03 = {
  kind: Other,
  amount: 2.03,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__other_0_01")
let other_0_01 = {
  kind: Other,
  amount: 0.01,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__other_6_66")
let other_6_66 = {
  kind: Other,
  amount: 6.66,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}

@genType.as("fees__other_error_negative_10_01")
let other_error_negative_10_01 = {
  kind: Other,
  amount: -10.01,
  id: makeUUID(),
  formattedAmount: None,
  totalAmount: None,
  formattedTotalAmount: None,
}
