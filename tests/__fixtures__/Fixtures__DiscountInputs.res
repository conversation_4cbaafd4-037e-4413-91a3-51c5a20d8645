open Accounting__Types

// Percent discounts
@genType.as("discountinputs__percent_20")
let percent_20 = {
  id: None,
  name: "Remise 20%",
  kind: Percent,
  value: 20.,
  quantity: 0,
}

@genType.as("discountinputs__percent_3_33")
let percent_3_33 = {
  id: None,
  name: "Remise 3.33%",
  kind: Percent,
  value: 3.33,
  quantity: 0,
}

@genType.as("discountinputs__percent_error_100_09")
let percent_error_100_09 = {
  id: None,
  name: "Remise 100.09% -- Error",
  kind: Percent,
  value: 100.09,
  quantity: 0,
}

@genType.as("discountinputs__percent_error_negative_75")
let percent_error_negative_75 = {
  id: None,
  name: "Remise -75% -- Error",
  kind: Percent,
  value: -75.,
  quantity: 0,
}

// Currency discounts
@genType.as("discountinputs__currency_0_33")
let currency_0_33 = {
  id: None,
  name: "Remise 0.33 currency",
  kind: Currency,
  value: 0.33,
  quantity: 0,
}

@genType.as("discountinputs__currency_6_66")
let currency_6_66 = {
  id: None,
  name: "Remise 6.66 currency",
  kind: Currency,
  value: 6.66,
  quantity: 0,
}

@genType.as("discountinputs__currency_0_001")
let currency_0_001 = {
  id: None,
  name: "Remise 0.001 currency",
  kind: Currency,
  value: 0.001,
  quantity: 0,
}

@genType.as("discountinputs__currency_12_09")
let currency_12_09 = {
  id: None,
  name: "Remise 12.09 currency",
  kind: Currency,
  value: 12.09,
  quantity: 0,
}

@genType.as("discountinputs__currency_0_01")
let currency_0_01 = {
  id: None,
  name: "Remise 0.01 currency",
  kind: Currency,
  value: 0.01,
  quantity: 0,
}

@genType.as("discountinputs__currency_error_negative_0_99")
let currency_error_negative_0_99 = {
  id: None,
  name: "Remise -0.99 currency",
  kind: Currency,
  value: -0.99,
  quantity: 0,
}

// Free discounts
@genType.as("discountinputs__free_3")
let free_3 = {
  id: None,
  name: "Remise de 3 gratuits",
  kind: Free,
  value: 0.,
  quantity: 3,
}

@genType.as("discountinputs__free_0")
let free_0 = {
  id: None,
  name: "Remise de 0 gratuit",
  kind: Free,
  value: 0.,
  quantity: 0,
}

@genType.as("discountinputs__free_error_negative_7")
let free_error_negative_7 = {
  id: None,
  name: "Remise de -7 gratuit",
  kind: Free,
  value: 0.,
  quantity: -7,
}
