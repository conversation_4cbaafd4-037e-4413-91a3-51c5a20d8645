open Accounting__Types

// Transport fees
@genType.as("feeinputs__transport_32_01")
let transport_32_01 = {id: None, kind: Transport, amount: 32.01}

@genType.as("feeinputs__transport_6_66")
let transport_6_66 = {id: None, kind: Transport, amount: 6.66}

@genType.as("feeinputs__transport_0_93")
let transport_0_93 = {id: None, kind: Transport, amount: 0.93}

@genType.as("feeinputs__transport_1_09")
let transport_1_09 = {id: None, kind: Transport, amount: 1.09}

@genType.as("feeinputs__transport_1_999")
let transport_1_999 = {id: None, kind: Transport, amount: 1.999}

@genType.as("feeinputs__transport_error_negative_0_01")
let transport_error_negative_0_01 = {
  id: None,
  kind: Transport,
  amount: -0.01,
}

// Taxes fees
@genType.as("feeinputs__tax_11_9")
let tax_11_9 = {id: None, kind: Taxes, amount: 11.9}

@genType.as("feeinputs__tax_0_09")
let tax_0_09 = {id: None, kind: Taxes, amount: 0.09}

@genType.as("feeinputs__tax_3_33")
let tax_3_33 = {id: None, kind: Taxes, amount: 3.33}

@genType.as("feeinputs__tax_0_001")
let tax_0_001 = {id: None, kind: Taxes, amount: 0.001}

@genType.as("feeinputs__tax_error_negative_0_09")
let tax_error_negative_0_09 = {id: None, kind: Taxes, amount: -0.09}

// Others fees
@genType.as("feeinputs__other_2_03")
let other_2_03 = {id: None, kind: Other, amount: 2.03}

@genType.as("feeinputs__other_0_01")
let other_0_01 = {id: None, kind: Other, amount: 0.01}

@genType.as("feeinputs__other_6_66")
let other_6_66 = {id: None, kind: Other, amount: 6.66}

@genType.as("feeinputs__other_1_901")
let other_1_901 = {id: None, kind: Other, amount: 1.901}

@genType.as("feeinputs__other_error_negative_10_01")
let other_error_negative_10_01 = {id: None, kind: Other, amount: -10.01}
