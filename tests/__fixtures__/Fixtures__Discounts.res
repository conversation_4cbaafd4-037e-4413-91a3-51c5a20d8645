open Accounting__Types
open Accounting__Utils

// Percent discounts
@genType.as("discounts__percent_20")
let percent_20 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise 20%",
  kind: Percent,
  value: 20.,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__percent_3_33")
let percent_3_33 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise 3.33%",
  kind: Percent,
  value: 3.33,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__percent_error_100_09")
let percent_error_100_09 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise 100.09% -- Error",
  kind: Percent,
  value: 100.09,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__percent_error_negative_75")
let percent_error_negative_75 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise -75% -- Error",
  kind: Percent,
  value: -75.,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

// Currency discounts
@genType.as("discounts__currency_0_33")
let currency_0_33 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise 0.33 currency",
  kind: Currency,
  value: 0.33,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__currency_6_66")
let currency_6_66 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise 6.66 currency",
  kind: Currency,
  value: 6.66,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__currency_12_09")
let currency_12_09 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise 12.09 currency",
  kind: Currency,
  value: 12.09,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__currency_0_01")
let currency_0_01 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise 0.01 currency",
  kind: Currency,
  value: 0.01,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__currency_error_negative_0_99")
let currency_error_negative_0_99 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise -0.99 currency",
  kind: Currency,
  value: -0.99,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

// Free discounts
@genType.as("discounts__free_3")
let free_3 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise de 3 gratuits",
  kind: Free,
  value: 0.,
  quantity: 3.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__free_0")
let free_0 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise de 0 gratuit",
  kind: Free,
  value: 0.,
  quantity: 0.->Big.fromFloat,
  warnings: [],
}

@genType.as("discounts__free_error_negative_7")
let free_error_negative_7 = {
  id: makeUUID(),
  formattedValue: None,
  amount: None,
  formattedAmount: None,
  name: "Remise de -7 gratuit",
  kind: Free,
  value: 0.,
  quantity: -7.->Big.fromFloat,
  warnings: [],
}
