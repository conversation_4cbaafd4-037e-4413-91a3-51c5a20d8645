open Accounting__Types
open Accounting__Utils

@genType
let makeUnitBase = (~index): productStruct<int> => {
  // Common values for all products should be generated
  identifier: Some(makeUUID()),
  stockKeepingUnit: Some(Js.Date.now()->Js.Float.toString),
  name: `Product ${index->string_of_int}`,
  description: `Description of product ${index->string_of_int}`,
  fees: [],
  discounts: [],
  id: makeUUID(),
  // Will be erase after generation
  expectedQuantity: 0,
  expectedQuantityWarning: [],
  packaging: None,
  quantity: 0,
  unitPrice: 0.,
  stock: 0,
  capacityUnit: None,
  // Recalculated fields without global discounts
  availablesFeeKinds: None,
  totalPrice: None,
  totalLocalDiscounts: None,
  unitFee: None,
  totalFees: None,
  totalAmountExcludingGlobalDiscounts: None,
  // With global discounts
  totalGlobalDiscounts: None,
  totalDiscounts: None,
  totalTaxesExcludingGlobalDiscount: None,
  totalTaxes: None,
  taxes: None,
  totalAmountExcludingTaxes: None,
  totalAmountIncludingTaxes: None,
  unitCost: None,
  // Formatted fields
  formattedStock: None,
  formattedQuantity: None,
  formattedExpectedQuantity: None,
  formattedUnitPrice: None,
  formattedTotalPrice: None,
  formattedUnitFee: None,
  formattedTotalFees: None,
  formattedTotalLocalDiscounts: None,
  formattedTotalDiscounts: None,
  formattedTotalAmountExcludingGlobalDiscounts: None,
  formattedTotalAmountExcludingTaxes: None,
  formattedTotalAmountIncludingTaxes: None,
}

@genType.as("products__quantity_3_unit_price_1_33_tax_20")
let quantity_3_unit_price_1_33_tax_20: product = Unit({
  ...makeUnitBase(~index=1),
  expectedQuantity: 3,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 3,
  unitPrice: 1.33,
  stock: 10,
  taxes: Some([
    {
      rate: 20.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__quantity_1_unit_price_0_091_tax_5_5")
let quantity_1_unit_price_0_09_tax_5_5: product = Unit({
  ...makeUnitBase(~index=2),
  expectedQuantity: 1,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 1,
  unitPrice: 0.091,
  stock: 10,
  taxes: Some([
    {
      rate: 5.5,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__quantity_10_unit_price_10_01_tax_20")
let quantity_10_unit_price_10_01_tax_20: product = Unit({
  ...makeUnitBase(~index=3),
  expectedQuantity: 10,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 10,
  unitPrice: 10.01,
  stock: 3,
  taxes: Some([
    {
      rate: 20.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__quantity_10_unit_price_0_0667_tax_5_5")
let quantity_10_unit_price_0_0667_tax_5_5: product = Unit({
  ...makeUnitBase(~index=7),
  expectedQuantity: 10,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 10,
  unitPrice: 0.6667,
  stock: -101,
  taxes: Some([
    {
      rate: 5.5,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__quantity_0_unit_price_20_1281_tax_20")
let quantity_0_unit_price_20_1281_tax_20: product = Unit({
  ...makeUnitBase(~index=8),
  expectedQuantity: 0,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 0,
  unitPrice: 20.1281,
  stock: -1,
  taxes: Some([
    {
      rate: 20.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__quantity_13_unit_price_20_119_tax_10")
let quantity_13_unit_price_20_11_tax_10: product = Unit({
  ...makeUnitBase(~index=9),
  expectedQuantity: 13,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 13,
  unitPrice: 20.119,
  stock: 3,
  taxes: Some([
    {
      rate: 10.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__quantity_3_unit_price_0_109_tax_2_1")
let quantity_3_unit_price_0_19_tax_2_1: product = Unit({
  ...makeUnitBase(~index=10),
  expectedQuantity: 3,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 3,
  unitPrice: 0.109,
  stock: 33,
  taxes: Some([
    {
      rate: 2.1,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__error_negative_unit_price")
let error_negative_unit_price: product = Unit({
  ...makeUnitBase(~index=4),
  expectedQuantity: 10,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 10,
  unitPrice: -0.01,
  stock: 7,
  taxes: Some([
    {
      rate: 5.5,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__error_negative_quantity")
let error_negative_quantity: product = Unit({
  ...makeUnitBase(~index=5),
  expectedQuantity: -3,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: -3,
  unitPrice: 99.33,
  stock: 10,
  taxes: Some([
    {
      rate: 20.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__error_negative_tax")
let error_negative_tax: product = Unit({
  ...makeUnitBase(~index=6),
  expectedQuantity: 3,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 3,
  unitPrice: 10.66,
  stock: 1,
  taxes: Some([
    {
      rate: -20.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__quantity_zero")
let quantity_zero: product = Unit({
  ...makeUnitBase(~index=11),
  expectedQuantity: 0,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 0,
  unitPrice: 220.66,
  stock: 13,
  taxes: Some([
    {
      rate: 5.5,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__error_empty_name")
let error_empty_name: product = Unit({
  ...makeUnitBase(~index=12),
  name: "",
  expectedQuantity: 23,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 23,
  unitPrice: 10.09,
  stock: 3,
  taxes: Some([
    {
      rate: 7.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

@genType.as("products__error_empty_description")
let error_empty_description: product = Unit({
  ...makeUnitBase(~index=13),
  description: "",
  expectedQuantity: 1,
  expectedQuantityWarning: [],
  packaging: Some(1),
  quantity: 1,
  unitPrice: 0.09,
  stock: 10,
  taxes: Some([
    {
      rate: 20.,
      amount: None,
      formattedAmount: None,
    },
  ]),
})

// Generate a product
@genType
let make = (product: product, ~discounts: array<discount>=[], ~fees: array<fee>=[], ()): product =>
  switch product {
  | Unit(product) =>
    Unit({
      ...product,
      discounts,
      fees,
    })
  | Bulk(product, precision) =>
    Bulk(
      {
        ...product,
        discounts,
        fees,
      },
      precision,
    )
  }
