open Accounting__Types
open Accounting__Utils

@genType
let makeUnitProductInputBase = (~index): productInputStruct<int> => {
  // Common values for all products should be generated
  id: None,
  identifier: Some(makeUUID()),
  stockKeepingUnit: Some(Js.Date.now()->Js.Float.toString),
  name: `Product ${index->string_of_int}`,
  description: `Description of product ${index->string_of_int}`,
  fees: [],
  discounts: [],
  // Will be erase after generation
  expectedQuantity: None,
  quantity: 0,
  packaging: None,
  unitPrice: 0.,
  stock: 0,
  tax: 0.,
  capacityUnit: None,
}

@genType
let makeBulkProductInputBase = (~index): productInputStruct<float> => {
  // Common values for all products should be generated
  id: None,
  identifier: Some(makeUUID()),
  stockKeepingUnit: Some(Js.Date.now()->Js.Float.toString),
  name: `Product ${index->string_of_int}`,
  description: `Description of product ${index->string_of_int}`,
  fees: [],
  discounts: [],
  // Will be erase after generation
  expectedQuantity: None,
  quantity: 0.,
  packaging: None,
  unitPrice: 0.,
  stock: 0.,
  tax: 0.,
  capacityUnit: None,
}

@genType.as("productinputs__quantity_3_unit_price_1_33_tax_20")
let quantity_3_unit_price_1_33_tax_20: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=1),
    quantity: 3,
    packaging: Some(1),
    unitPrice: 1.33,
    stock: 10,
    tax: 20.,
  },
})

@genType.as("productinputs__quantity_1_unit_price_0_09_tax_5_5")
let quantity_1_unit_price_0_09_tax_5_5: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=2),
    quantity: 1,
    packaging: Some(1),
    unitPrice: 0.09,
    stock: 10,
    tax: 5.5,
  },
})

@genType.as("productinputs__quantity_10_unit_price_10_01_tax_20")
let quantity_10_unit_price_10_01_tax_20: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=3),
    quantity: 10,
    packaging: Some(7),
    unitPrice: 10.01,
    stock: 3,
    tax: 20.,
  },
})

@genType.as("productinputs__quantity_10_unit_price_0_0667_tax_5_5")
let quantity_10_unit_price_0_0667_tax_5_5: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=7),
    quantity: 10,
    packaging: Some(2),
    unitPrice: 0.6667,
    stock: -101,
    tax: 5.5,
  },
})

@genType.as("productinputs__quantity_0_unit_price_20_1281_tax_20")
let quantity_0_unit_price_20_1281_tax_20: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=8),
    quantity: 0,
    packaging: Some(1),
    unitPrice: 20.1281,
    stock: -1,
    tax: 20.,
  },
})

@genType.as("productinputs__quantity_13_unit_price_20_11_tax_10")
let quantity_13_unit_price_20_11_tax_10: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=9),
    quantity: 13,
    packaging: Some(3),
    unitPrice: 20.11,
    stock: 3,
    tax: 10.,
  },
})

@genType.as("productinputs__quantity_3_unit_price_0_19_tax_2_1")
let quantity_3_unit_price_0_19_tax_2_1: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=10),
    quantity: 3,
    packaging: Some(3),
    unitPrice: 0.19,
    stock: 33,
    tax: 2.1,
  },
})

@genType.as("productinputs__quantity_301_unit_price_19_881_tax_20")
let quantity_301_unit_price_19_881_tax_20: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=18),
    quantity: 301,
    packaging: Some(3),
    unitPrice: 19.881,
    stock: 0,
    tax: 20.,
  },
})

@genType.as("productinputs__bulk_kilogram_3_quantity_420900_unit_price_11_99_tax_5_5")
let bulk_kilogram_3_quantity_420900_unit_price_11_99_tax_5_5: productInput = Bulk({
  product: {
    ...makeBulkProductInputBase(~index=14),
    quantity: 420.9,
    unitPrice: 11.99,
    stock: 13.,
    tax: 5.5,
    packaging: Some(1.),
    capacityUnit: Some("kg"),
  },
  precision: 3,
})

@genType.as("productinputs__bulk_liter_2_quantity_6935_unit_price_3_90_tax_5_5")
let bulk_liter_2_quantity_6935_unit_price_3_90_tax_5_5: productInput = Bulk({
  product: {
    ...makeBulkProductInputBase(~index=15),
    quantity: 69.35,
    unitPrice: 3.90,
    stock: 30.,
    tax: 5.5,
    packaging: Some(1.),
    capacityUnit: Some("L"),
  },
  precision: 2,
})

@genType.as("productinputs__bulk_gram_3_quantity_25_unit_price_5_69_tax_20")
let bulk_gram_3_quantity_25_unit_price_5_69_tax_20: productInput = Bulk({
  product: {
    ...makeBulkProductInputBase(~index=16),
    quantity: 25.,
    unitPrice: 5.69,
    stock: 120.,
    tax: 20.,
    packaging: Some(1.),
    capacityUnit: Some("g"),
  },
  precision: 3,
})

@genType.as("productinputs__bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20")
let bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20: productInput = Bulk({
  product: {
    ...makeBulkProductInputBase(~index=17),
    quantity: 5.,
    unitPrice: 3.339,
    stock: 12.,
    tax: 20.,
    packaging: Some(1.),
    capacityUnit: Some("kg"),
  },
  precision: 2,
})

@genType.as("productinputs__error_negative_unit_price")
let error_negative_unit_price: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=4),
    quantity: 10,
    packaging: Some(10),
    unitPrice: -0.01,
    stock: 7,
    tax: 5.5,
  },
})

@genType.as("productinputs__error_negative_quantity")
let error_negative_quantity: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=5),
    quantity: -3,
    packaging: Some(2),
    unitPrice: 99.33,
    stock: 10,
    tax: 20.,
  },
})

@genType.as("productinputs__error_negative_tax")
let error_negative_tax: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=6),
    quantity: 3,
    packaging: Some(1),
    unitPrice: 10.66,
    stock: 1,
    tax: -20.,
  },
})

@genType.as("productinputs__quantity_zero")
let quantity_zero: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=11),
    quantity: 0,
    packaging: Some(2),
    unitPrice: 220.66,
    stock: 13,
    tax: 5.5,
  },
})

@genType.as("productinputs__error_empty_name")
let error_empty_name: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=12),
    name: "",
    quantity: 23,
    packaging: Some(3),
    unitPrice: 10.09,
    stock: 3,
    tax: 7.,
  },
})

@genType.as("productinputs__error_empty_description")
let error_empty_description: productInput = Unit({
  product: {
    ...makeUnitProductInputBase(~index=13),
    description: "",
    quantity: 1,
    packaging: Some(3),
    unitPrice: 0.09,
    stock: 10,
    tax: 20.,
  },
})

// Generate a product input
@genType
let make = (
  productInput: productInput,
  ~id=?,
  ~discounts: array<discountInput>=[],
  ~fees: array<feeInput>=[],
  (),
): productInput =>
  switch productInput {
  | Unit({product: productInput}) =>
    Unit({
      product: {
        ...productInput,
        id: id->Option.getWithDefault(productInput.id),
        discounts,
        fees,
      },
    })
  | Bulk({product: productInput, precision}) =>
    Bulk({
      product: {
        ...productInput,
        id: id->Option.getWithDefault(productInput.id),
        discounts,
        fees,
      },
      precision,
    })
  }
