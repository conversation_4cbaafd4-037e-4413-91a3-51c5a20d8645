open Jest
open Fixtures
open Accounting__Types

module Maker = Accounting__Maker
module ComputerProduct = Accounting__Computer__Product
module Computer = Accounting__Computer

describe("ComputeProduct", () => {
  open Expect
  open ComputerProduct

  let productInput = {
    open ProductInputs
    make(
      quantity_10_unit_price_10_01_tax_20,
      ~discounts=[DiscountInputs.currency_0_33],
      ~fees=[FeeInputs.transport_6_66],
      (),
    )
  }

  let bulkProductInputA = {
    open ProductInputs
    make(bulk_kilogram_3_quantity_420900_unit_price_11_99_tax_5_5, ~discounts=[], ~fees=[], ())
  }

  let bulkProductInputB = {
    open ProductInputs
    make(bulk_liter_2_quantity_6935_unit_price_3_90_tax_5_5, ~discounts=[], ~fees=[], ())
  }

  let bulkProductInputC = {
    open ProductInputs
    make(bulk_gram_3_quantity_25_unit_price_5_69_tax_20, ~discounts=[], ~fees=[], ())
  }

  let product = ref(productInput->Maker.Product.make)
  let initialCart =
    {
      products: [productInput],
      discounts: [DiscountInputs.percent_3_33],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }
    ->Maker.Cart.make
    ->Computer.make
  let cartWithBulkProducts =
    {
      products: [productInput, bulkProductInputA, bulkProductInputB, bulkProductInputC],
      discounts: [DiscountInputs.currency_12_09],
      decimalPrecision: 2,
      currency: Eur,
      taxesFree: false,
      standardTaxRate: 20.,
    }
    ->Maker.Cart.make
    ->Computer.make

  describe("computeTotalPrice", () => {
    product := product.contents->computeTotalPrice(~cart=cartWithBulkProducts)

    test(
      "should return compute total price",
      () =>
        expect(
          switch product.contents {
          | Unit({totalPrice}) | Bulk({totalPrice}, _) => totalPrice->Option.getExn
          },
        ) |> toEqual(100.1->Big.fromFloat),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeExpectedQuantityWarning", () => {
    product := product.contents->computeExpectedQuantityWarning

    test(
      "should compute expected quantity warning",
      () =>
        expect(
          switch product.contents {
          | Unit({expectedQuantityWarning})
          | Bulk({expectedQuantityWarning}, _) => expectedQuantityWarning
          },
        ) |> toEqual([IsNotMultipleOfPackaging]),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeTotalLocalDiscounts", () => {
    product :=
      product.contents
      ->computeLocalDiscounts(~cart=cartWithBulkProducts)
      ->computeTotalLocalDiscounts(~cart=cartWithBulkProducts)

    test(
      "should return correct totalLocalDiscounts value",
      () =>
        expect(
          switch product.contents {
          | Unit({totalLocalDiscounts}) | Bulk({totalLocalDiscounts}, _) =>
            totalLocalDiscounts->Option.getExn
          },
        ) |> toEqual(0.33->Big.fromFloat),
    )
    test(
      "should match snapshot",
      () =>
        expect(
          product.contents
          ->computeLocalDiscounts(~cart=cartWithBulkProducts)
          ->computeTotalLocalDiscounts(~cart=cartWithBulkProducts),
        ) |> toMatchSnapshot,
    )
  })

  describe("computeUnitFee", () => {
    product := product.contents->computeFees->computeUnitFee(~cart=cartWithBulkProducts)

    test(
      "should return correct unitFees value",
      () =>
        expect(
          switch product.contents {
          | Unit({unitFee}) | Bulk({unitFee}, _) => unitFee->Option.getExn
          },
        ) |> toEqual(6.66->Big.fromFloat),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeTotalFees", () => {
    product := product.contents->computeTotalFees(~cart=cartWithBulkProducts)

    test(
      "should output correct value of totalFees amount - after computeUnitFee",
      () =>
        expect(
          switch product.contents {
          | Unit({totalFees}) | Bulk({totalFees}, _) => totalFees->Option.getExn
          },
        ) |> toEqual(66.6->Big.fromFloat),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeTotalAmountExcludingGlobalDiscounts", () => {
    product :=
      product.contents->computeTotalAmountExcludingGlobalDiscounts(~cart=cartWithBulkProducts)

    test(
      "should compute total amount of product without global discounts",
      () =>
        expect(
          switch product.contents {
          | Unit({totalAmountExcludingGlobalDiscounts})
          | Bulk({totalAmountExcludingGlobalDiscounts}, _) =>
            totalAmountExcludingGlobalDiscounts->Option.getExn
          },
        ) |> toEqual(166.37->Big.fromFloat),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeGlobalDiscounts", () => {
    product := product.contents->computeGlobalDiscounts(~cart=initialCart)

    test(
      "should compute correct value of global discounts amounts based on scoped product",
      () =>
        expect(
          switch product.contents {
          | Unit({totalGlobalDiscounts}) | Bulk({totalGlobalDiscounts}, _) =>
            totalGlobalDiscounts->Option.getExn
          },
        ) |> toEqual(3.32->Big.fromFloat),
    )
    test("should math snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeTotalDiscounts", () => {
    product := product.contents->computeTotalDiscounts

    test(
      "should output correct value of total (global + local) discounts amounts",
      () =>
        expect(
          switch product.contents {
          | Unit({totalDiscounts}) | Bulk({totalDiscounts}, _) => totalDiscounts->Option.getExn
          },
        ) |> toEqual(3.65->Big.fromFloat),
    )
  })

  describe("computeTotalAmounts", () => {
    product := product.contents->ComputerProduct.computeTotalAmounts(~cart=initialCart)

    test(
      "should compute total amounts without taxes",
      () =>
        expect(
          switch product.contents {
          | Unit({totalAmountExcludingTaxes, totalAmountIncludingTaxes})
          | Bulk({totalAmountExcludingTaxes, totalAmountIncludingTaxes}, _) => (
              totalAmountExcludingTaxes,
              totalAmountIncludingTaxes,
            )
          },
        ) |> toEqual((Some(163.05->Big.fromFloat), Some(195.66->Big.fromFloat))),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeUnitCost", () => {
    product := product.contents->computeUnitCost(~cart=initialCart)

    test(
      "should compute unit cost",
      () =>
        expect(
          switch product.contents {
          | Unit({unitCost}) | Bulk({unitCost}, _) => unitCost
          },
        ) |> toEqual(Some(16.31->Big.fromFloat)),
    )
    test(
      "should not compute unit cost when the quantity is not positive",
      () => {
        let product = switch product.contents {
        | Unit(product) => Unit({...product, quantity: 0})
        | Bulk(product, precision) => Bulk({...product, quantity: 0.->Big.fromFloat}, precision)
        }->computeUnitCost(~cart=initialCart)

        expect(
          switch product {
          | Unit({unitCost}) | Bulk({unitCost}, _) => unitCost
          },
        ) |> toEqual(None)
      },
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeTaxesAmounts", () => {
    product := product.contents->computeTaxesAmounts(~cart=initialCart)

    test(
      "should have only one tax",
      () =>
        expect(
          switch product.contents {
          | Unit({taxes}) | Bulk({taxes}, _) => taxes->Option.getExn
          },
        ) |> toHaveLength(1),
    )
    test(
      "should correctly compute tax amount",
      () =>
        expect(
          switch product.contents {
          | Unit({taxes}) | Bulk({taxes}, _) => (taxes->Option.getExn->Array.getExn(0)).amount
          },
        ) |> toEqual(Some(32.61->Big.fromFloat)),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)

    describe(
      "with cart taxesFree enabled",
      () => {
        let productTaxes = switch product.contents->computeTaxesAmounts(
          ~cart={...initialCart, taxesFree: true},
        ) {
        | Unit({taxes}) | Bulk({taxes}, _) => taxes
        }

        test(
          "should return taxes with values of 0",
          () => {
            expect(productTaxes) |> toEqual(
              Some([
                {
                  rate: 20.,
                  amount: Some(0.->Big.fromFloat),
                  formattedAmount: None,
                },
              ]),
            )
          },
        )
      },
    )
  })

  describe("computeTotalTaxesExcludingGlobalDiscount", () => {
    product := product.contents->computeTotalTaxesExcludingGlobalDiscount(~cart=initialCart)

    test(
      "should compute total amount of taxes excluding global discount",
      () =>
        expect(
          switch product.contents {
          | Unit({totalTaxesExcludingGlobalDiscount})
          | Bulk({totalTaxesExcludingGlobalDiscount}, _) =>
            totalTaxesExcludingGlobalDiscount->Option.getExn
          },
        ) |> toEqual(33.27->Big.fromFloat),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeTotalTaxes", () => {
    product := product.contents->computeTotalTaxes(~cart=initialCart)

    test(
      "should compute total amount of taxes",
      () =>
        expect(
          switch product.contents {
          | Unit({totalTaxes}) | Bulk({totalTaxes}, _) => totalTaxes->Option.getExn
          },
        ) |> toEqual(32.61->Big.fromFloat),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeTotalAmountWithTaxes", () => {
    product := product.contents->computeTotalAmountWithTaxes(~cart=initialCart)

    test(
      "should compute total amounts with taxes",
      () =>
        expect(
          switch product.contents {
          | Unit({totalAmountExcludingTaxes, totalAmountIncludingTaxes})
          | Bulk({totalAmountExcludingTaxes, totalAmountIncludingTaxes}, _) => (
              totalAmountExcludingTaxes,
              totalAmountIncludingTaxes,
            )
          },
        ) |> toEqual((Some(163.05->Big.fromFloat), Some(195.66->Big.fromFloat))),
    )
    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("preCompute", () => {
    product := productInput->Maker.Product.make
    product := product.contents->preCompute(~cart=cartWithBulkProducts)

    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("postCompute", () => {
    product := product.contents->postCompute(~cart=initialCart)

    test("should match snapshot", () => expect(product.contents) |> toMatchSnapshot)
  })

  describe("computeProduct bulk products", () => {
    let bulkProductA = bulkProductInputA->Maker.Product.make
    let product =
      bulkProductA->preCompute(~cart=cartWithBulkProducts)->postCompute(~cart=cartWithBulkProducts)

    test("should match snapshot", () => expect(product) |> toMatchSnapshot)
  })
})
