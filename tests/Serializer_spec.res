open Jest
open Fixtures
open Accounting__Types

module Serializer = Accounting__Serializer

describe("Serializer", () => {
  open Expect
  open! Expect.Operators
  open Serializer

  describe("with decimalPrecision set to 2", () => {
    let productInputA = ProductInputs.make(
      ProductInputs.quantity_10_unit_price_10_01_tax_20,
      ~discounts=[DiscountInputs.percent_20],
      ~fees=[FeeInputs.other_6_66, FeeInputs.tax_0_09, FeeInputs.transport_32_01],
      (),
    )
    let productInputB = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_1_33_tax_20,
      ~discounts=[DiscountInputs.free_0],
      ~fees=[FeeInputs.tax_0_09, FeeInputs.transport_1_09],
      (),
    )
    let productInputC = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_0_19_tax_2_1,
      ~fees=[FeeInputs.tax_3_33],
      (),
    )
    let productInputD = ProductInputs.make(
      ProductInputs.quantity_0_unit_price_20_1281_tax_20,
      ~discounts=[DiscountInputs.currency_6_66],
      (),
    )

    let cart =
      {
        products: [productInputA, productInputB, productInputC, productInputD],
        discounts: [],
        decimalPrecision: 2,
        currency: Eur,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make
      ->Formatter.Cart.make

    let cartSerialized = cart->serialize
    let cartDeserialized = cartSerialized->deserialize

    describe(
      "Currency",
      () => {
        test(
          "should serialize Eur currency input",
          () => expect(Eur->Currency.serialize) |> toBe("eur"),
        )
        test(
          "should serialize Usd currency input",
          () => expect(Usd->Currency.serialize) |> toBe("usd"),
        )
        test(
          "should deserialize Usd currency input",
          () => expect(Usd->Currency.serialize->Currency.deserialize) |> toBe(Usd),
        )
        test(
          "should deserialize Usd currency input",
          () => expect(Eur->Currency.serialize->Currency.deserialize) |> toBe(Eur),
        )
      },
    )

    describe(
      "Tax",
      () => {
        let serializedTax: SerializedTypes.tax = {rate: "20"}
        let deserializedTax: tax = {
          rate: 20.,
          amount: None,
          formattedAmount: None,
        }->Formatter.ProductTax.make(
          ~currency=cart.currency,
          ~precision=Some(cart.decimalPrecision),
        )

        test(
          "should deserialize input to retrieve productTax",
          () =>
            expect(
              serializedTax->Tax.deserialize(~decimalPrecision=cart.decimalPrecision),
            ) |> toEqual(deserializedTax),
        )

        test(
          "should deserialize input to retrieve productTax",
          () => expect(deserializedTax->Tax.serialize) |> toEqual(serializedTax),
        )
      },
    )

    describe(
      "Discount",
      () => {
        test(
          "should serialize Currency discount kind",
          () => expect(Currency->Discount.serializeKind) |> toBe("currency"),
        )
        test(
          "should serialize Percent discount kind",
          () => expect(Percent->Discount.serializeKind) |> toBe("percent"),
        )
        test(
          "should serialize Free discount kind",
          () => expect(Free->Discount.serializeKind) |> toBe("free"),
        )

        test(
          "should deserialize to retrieve Currency discount kind",
          () => expect("currency"->Discount.deserializeKind) |> toBe(Currency),
        )
        test(
          "should deserialize to retrieve Percent discount kind",
          () => expect("percent"->Discount.deserializeKind) |> toBe(Percent),
        )
        test(
          "should deserialize to retrieve Free discount kind",
          () => expect("free"->Discount.deserializeKind) |> toBe(Free),
        )

        test(
          "should deserialize a serialized discount",
          () => {
            let serializedDiscount: SerializedTypes.discount = {
              id: "123",
              name: "Fidelity",
              kind: "free",
              value: "0",
              quantity: 10,
            }
            let deserializedDiscount: discountInput = {
              id: Some("123"),
              name: "Fidelity",
              kind: Free,
              value: 0.,
              quantity: 10,
            }

            expect(serializedDiscount->Discount.deserialize) |> toEqual(deserializedDiscount)
          },
        )

        test(
          "should serialize a discount",
          () => {
            let deserializedDiscount: discount = {
              id: "123",
              name: "Fidelity",
              kind: Currency,
              value: 10.,
              formattedValue: Some("0"),
              quantity: 0.->Big.fromFloat,
              amount: Some(10.->Big.fromFloat),
              formattedAmount: Some("10"),
              warnings: [],
            }
            let serializedDiscount: SerializedTypes.discount = {
              id: "123",
              name: "Fidelity",
              kind: "currency",
              value: "10",
              quantity: 0,
            }

            expect(deserializedDiscount->Discount.serialize) |> toEqual(serializedDiscount)
          },
        )
      },
    )

    describe(
      "Fee",
      () => {
        test(
          "should serialize Transport fee kind",
          () => expect(Transport->Fee.serializeKind) |> toBe("transport"),
        )
        test(
          "should serialize Taxes fee kind",
          () => expect(Taxes->Fee.serializeKind) |> toBe("taxes"),
        )
        test(
          "should serialize Other fee kind",
          () => expect(Other->Fee.serializeKind) |> toBe("other"),
        )

        test(
          "should deserialize Transport fee kind",
          () => expect(Transport->Fee.serializeKind->Fee.deserializeKind) |> toBe(Transport),
        )
        test(
          "should deserialize Taxes fee kind",
          () => expect(Taxes->Fee.serializeKind->Fee.deserializeKind) |> toBe(Taxes),
        )
        test(
          "should deserialize Other fee kind",
          () => {
            let expectedResult: feeKind = Other

            expect(Other->Fee.serializeKind->Fee.deserializeKind) |> toBe(expectedResult)
          },
        )

        test(
          "should deserialize a serialized fee",
          () => {
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "other",
              amount: "13.23",
            }
            let feeInput: feeInput = {
              id: Some("123"),
              kind: Other,
              amount: 13.23,
            }

            expect(serializedFee->Fee.deserialize) |> toEqual(feeInput)
          },
        )
        test(
          "should serialize a fee",
          () => {
            let deserializedFee: fee = {
              id: "123",
              kind: Taxes,
              amount: 9.12,
              formattedAmount: Some("9.12"),
              totalAmount: None,
              formattedTotalAmount: None,
            }
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "taxes",
              amount: "9.12",
            }

            expect(deserializedFee->Fee.serialize) |> toEqual(serializedFee)
          },
        )
      },
    )

    describe(
      "Cart",
      () => {
        test("should correctly serialise input", () => expect(cartSerialized)->toMatchSnapshot)

        test(
          "should output correct currency value after deserialization",
          () => expect(cartDeserialized.currency) |> toBe(Eur),
        )

        test(
          "should have a product with correct fee kind value after deserialization",
          () => {
            let identifier = switch productInputC {
            | Unit({product: {identifier}}) | Bulk({product: {identifier}, _}) => identifier
            }
            let fees = switch cartDeserialized.products->Utils.getProductByKey(identifier) {
            | Unit({fees}) | Bulk({fees}, _) => fees
            }

            expect((fees->Array.getExn(0)).kind) |> toBe(Taxes)
          },
        )

        test(
          "should have a product with correct discount kind value after deserialization",
          () => {
            let stockKeepingUnit = switch productInputA {
            | Unit({product: {stockKeepingUnit}})
            | Bulk({product: {stockKeepingUnit}, _}) => stockKeepingUnit
            }
            let discounts = switch cartDeserialized.products->Utils.getProductByKey(
              stockKeepingUnit,
            ) {
            | Unit({discounts}) | Bulk({discounts}, _) => discounts
            }

            expect((discounts->Array.getExn(0)).kind) |> toBe(Percent)
          },
        )

        test("should correctly deserialise input", () => expect(cartDeserialized)->toMatchSnapshot)
      },
    )
  })

  describe("with decimalPrecision set to 3", () => {
    let productInputA = ProductInputs.make(
      ProductInputs.quantity_301_unit_price_19_881_tax_20,
      ~discounts=[DiscountInputs.currency_6_66],
      ~fees=[FeeInputs.other_6_66, FeeInputs.transport_32_01],
      (),
    )
    let productInputB = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_1_33_tax_20,
      ~discounts=[DiscountInputs.free_0],
      ~fees=[FeeInputs.tax_0_09, FeeInputs.transport_1_09],
      (),
    )
    let productInputC = ProductInputs.make(
      ProductInputs.quantity_3_unit_price_0_19_tax_2_1,
      ~fees=[FeeInputs.tax_3_33],
      (),
    )
    let bulkProductInputD = ProductInputs.make(
      ProductInputs.bulk_kilogram_2_quantity_5_unit_price_3_339_tax_20,
      ~discounts=[DiscountInputs.percent_20],
      (),
    )

    let cart =
      {
        products: [productInputA, productInputB, productInputC, bulkProductInputD],
        discounts: [],
        decimalPrecision: 3,
        currency: Usd,
        taxesFree: false,
        standardTaxRate: 20.,
      }
      ->Maker.Cart.make
      ->Computer.make
      ->Formatter.Cart.make

    let cartSerialized = cart->serialize
    let cartDeserialized = cartSerialized->deserialize

    describe(
      "Currency",
      () => {
        test(
          "should serialize Eur currency input",
          () => expect(Eur->Currency.serialize) |> toBe("eur"),
        )
        test(
          "should serialize Usd currency input",
          () => expect(Usd->Currency.serialize) |> toBe("usd"),
        )
        test(
          "should deserialize Usd currency input",
          () => expect(Usd->Currency.serialize->Currency.deserialize) |> toBe(Usd),
        )
        test(
          "should deserialize Usd currency input",
          () => expect(Eur->Currency.serialize->Currency.deserialize) |> toBe(Eur),
        )
      },
    )

    describe(
      "Tax",
      () => {
        let serializedTax: SerializedTypes.tax = {rate: "20"}
        let deserializedTax: tax = {
          rate: 20.,
          amount: None,
          formattedAmount: None,
        }->Formatter.ProductTax.make(
          ~currency=cart.currency,
          ~precision=Some(cart.decimalPrecision),
        )

        test(
          "should deserialize input to retrieve productTax",
          () =>
            expect(
              serializedTax->Tax.deserialize(~decimalPrecision=cart.decimalPrecision),
            ) |> toEqual(deserializedTax),
        )

        test(
          "should deserialize input to retrieve productTax",
          () => expect(deserializedTax->Tax.serialize) |> toEqual(serializedTax),
        )
      },
    )

    describe(
      "Discount",
      () => {
        test(
          "should serialize Currency discount kind",
          () => expect(Currency->Discount.serializeKind) |> toBe("currency"),
        )
        test(
          "should serialize Percent discount kind",
          () => expect(Percent->Discount.serializeKind) |> toBe("percent"),
        )
        test(
          "should serialize Free discount kind",
          () => expect(Free->Discount.serializeKind) |> toBe("free"),
        )

        test(
          "should deserialize to retrieve Currency discount kind",
          () => expect("currency"->Discount.deserializeKind) |> toBe(Currency),
        )
        test(
          "should deserialize to retrieve Percent discount kind",
          () => expect("percent"->Discount.deserializeKind) |> toBe(Percent),
        )
        test(
          "should deserialize to retrieve Free discount kind",
          () => expect("free"->Discount.deserializeKind) |> toBe(Free),
        )

        test(
          "should deserialize a serialized discount",
          () => {
            let serializedDiscount: SerializedTypes.discount = {
              id: "123",
              name: "Fidelity",
              kind: "free",
              value: "0",
              quantity: 10,
            }

            expect(serializedDiscount->Discount.deserialize) |> toEqual({
              id: Some("123"),
              name: "Fidelity",
              kind: Free,
              value: 0.,
              quantity: 10,
            })
          },
        )

        test(
          "should serialize a discount",
          () => {
            let deserializedDiscount: discount = {
              id: "123",
              name: "Fidelity",
              kind: Currency,
              value: 10.,
              formattedValue: Some("0"),
              quantity: 10.->Big.fromFloat,
              amount: Some(10.->Big.fromFloat),
              formattedAmount: Some("10"),
              warnings: [],
            }

            (expect(deserializedDiscount->Discount.serialize) |> toEqual({
              SerializedTypes.id: "123",
              name: "Fidelity",
              kind: "currency",
              value: "10",
              quantity: 10,
            }))->ignore

            expect(deserializedDiscount->Discount.serialize(~capacityPrecision=3)) |> toEqual({
              SerializedTypes.id: "123",
              name: "Fidelity",
              kind: "currency",
              value: "10",
              quantity: 10000,
            })
          },
        )
      },
    )

    describe(
      "Fee",
      () => {
        test(
          "should serialize Transport fee kind",
          () => expect(Transport->Fee.serializeKind) |> toBe("transport"),
        )
        test(
          "should serialize Taxes fee kind",
          () => expect(Taxes->Fee.serializeKind) |> toBe("taxes"),
        )
        test(
          "should serialize Other fee kind",
          () => expect(Other->Fee.serializeKind) |> toBe("other"),
        )

        test(
          "should deserialize Transport fee kind",
          () => expect(Transport->Fee.serializeKind->Fee.deserializeKind) |> toBe(Transport),
        )
        test(
          "should deserialize Taxes fee kind",
          () => expect(Taxes->Fee.serializeKind->Fee.deserializeKind) |> toBe(Taxes),
        )
        test(
          "should deserialize Other fee kind",
          () => {
            let expectedResult: feeKind = Other

            expect(Other->Fee.serializeKind->Fee.deserializeKind) |> toBe(expectedResult)
          },
        )

        test(
          "should deserialize a serialized fee",
          () => {
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "other",
              amount: "13.23",
            }
            let feeInput: feeInput = {
              id: Some("123"),
              kind: Other,
              amount: 13.23,
            }

            expect(serializedFee->Fee.deserialize) |> toEqual(feeInput)
          },
        )
        test(
          "should serialize a fee",
          () => {
            let deserializedFee: fee = {
              id: "123",
              kind: Taxes,
              amount: 9.12,
              formattedAmount: Some("9.12"),
              totalAmount: None,
              formattedTotalAmount: None,
            }
            let serializedFee: SerializedTypes.fee = {
              id: "123",
              kind: "taxes",
              amount: "9.12",
            }

            expect(deserializedFee->Fee.serialize) |> toEqual(serializedFee)
          },
        )
      },
    )

    describe(
      "Cart",
      () => {
        test("should correctly serialise input", () => expect(cartSerialized)->toMatchSnapshot)

        test(
          "should output correct currency value after deserialization",
          () => expect(cartDeserialized.currency) |> toBe(Usd),
        )

        test(
          "should have a product with correct fee kind value after deserialization",
          () => {
            let identifier = switch productInputC {
            | Unit({product: {identifier}}) | Bulk({product: {identifier}, _}) => identifier
            }
            let fees = switch cartDeserialized.products->Utils.getProductByKey(identifier) {
            | Unit({fees}) | Bulk({fees}, _) => fees
            }

            expect((fees->Array.getExn(0)).kind) |> toBe(Taxes)
          },
        )

        test(
          "should have a product with correct discount kind value after deserialization",
          () => {
            let stockKeepingUnit = switch productInputA {
            | Unit({product: {stockKeepingUnit}})
            | Bulk({product: {stockKeepingUnit}, _}) => stockKeepingUnit
            }
            let discounts = switch cartDeserialized.products->Utils.getProductByKey(
              stockKeepingUnit,
            ) {
            | Unit({discounts}) | Bulk({discounts}, _) => discounts
            }

            expect((discounts->Array.getExn(0)).kind) |> toBe(Currency)
          },
        )

        test("should correctly deserialise input", () => expect(cartDeserialized)->toMatchSnapshot)
      },
    )
  })

  describe("cart normalization", () => {
    describe(
      "with a serialized cart which doesn't have decimalPrecision",
      () => {
        let serializedCartWithoutDecimalPrecision = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3,
              "packaging": 7,
              "expectedQuantity": 10,
              "quantity": 10,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "bulk": true,
              "capacityPrecision": 0
            }
          ],
          "discounts": [],
          "currency": "eur",
          "taxesIncluded": false,
          "taxesFree": false
        }
      `

        test(
          "cart should have decimalPrecision after deserialization",
          () =>
            expect((serializedCartWithoutDecimalPrecision->deserialize).decimalPrecision) |> toBe(
              5,
            ),
        )
      },
    )

    describe(
      "with a serialized cart which have wrong bulk value",
      () => {
        let serializedCartWithWrongBulkValue = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3,
              "packaging": 7,
              "expectedQuantity": 10,
              "quantity": 10,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "bulk": {
                "wrongBulkField": true,
                "anotherWrongField": 1000
              },
              "capacityPrecision": 0
            }
          ],
          "discounts": [],
          "currency": "eur",
          "taxesIncluded": false,
          "taxesFree": false
        }
      `

        test(
          "cart should have falsy bulk after deserialization",
          () => expect(serializedCartWithWrongBulkValue->deserialize)->toMatchSnapshot,
        )
      },
    )

    describe(
      "with a serialized cart which doesn't have bulk field",
      () => {
        let serializedCartWithoutBulkField = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3,
              "packaging": 7,
              "expectedQuantity": 10,
              "quantity": 10,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "capacityPrecision": 0
            }
          ],
          "discounts": [],
          "currency": "eur",
          "taxesIncluded": false,
          "taxesFree": false
        }
      `

        test(
          "bulk field should be added and have a falsy value after deserialization",
          () => expect(serializedCartWithoutBulkField->deserialize)->toMatchSnapshot,
        )
      },
    )

    test(
      "should match snapshot",
      () => {
        let serializedCart = `
        {
          "products": [
            {
              "id": "v4-uuid-48",
              "identifier": "v4-uuid-3",
              "stockKeepingUnit": "1479427200003",
              "name": "Product 3",
              "description": "Description of product 3",
              "unitPrice": "10.01",
              "stock": 3500,
              "packaging": 2000,
              "expectedQuantity": 10000,
              "quantity": 10000,
              "fees": [
                {
                  "id": "v4-uuid-44",
                  "kind": "other",
                  "amount": "6.66"
                },
                {
                  "id": "v4-uuid-45",
                  "kind": "taxes",
                  "amount": "0.09"
                },
                {
                  "id": "v4-uuid-46",
                  "kind": "transport",
                  "amount": "32.01"
                }
              ],
              "discounts": [
                {
                  "id": "v4-uuid-47",
                  "name": "Remise 20%",
                  "kind": "percent",
                  "value": "20",
                  "quantity": 0
                }
              ],
              "taxes": [
                {
                  "rate": "20",
                  "ratio": "1"
                }
              ],
              "capacityPrecision": 3,
              "bulk": true
            }
          ],
          "discounts": [],
          "currency": "eur"
        }
      `

        expect(serializedCart->deserialize)->toMatchSnapshot
      },
    )
  })
})
